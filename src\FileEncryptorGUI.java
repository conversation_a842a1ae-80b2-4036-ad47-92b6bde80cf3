import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.SecureRandom;
import java.util.Base64;
import javax.imageio.ImageIO;

public class FileEncryptorGUI extends JFrame {

    private static final String[] ALGORITHMS = {"AES", "DES", "Blowfish"};
    private static final int BUFFER_SIZE = 8192; // 8KB buffer for file operations

    private String currentAlgorithm = "AES";
    private int currentKeySize = 256;

    private JTextField inputFileField;
    private JTextField keyFileField;
    private JTextField encryptedFileField;
    private JTextField decryptedFileField;
    private JButton generateKeyButton, encryptButton, decryptButton;
    private JButton browseInputButton;
    private JButton showKeyButton;
    private JButton downloadDecryptedButton;
    private JTextArea keyDisplayArea, keyInputArea, previewTextArea;
    private JLabel keyValidationLabel;
    private JProgressBar progressBar;
    private JLabel statusLabel;
    private JComboBox<String> algorithmComboBox;
    private JComboBox<Integer> keySizeComboBox;
    private JLabel imagePreviewLabel;
    private JScrollPane imageScrollPane;
    private String lastGeneratedKey;
    private String lastDecryptedFile;

    public FileEncryptorGUI() {
        setTitle("Simple File Encryptor");
        setSize(600, 400);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);

        // Initialize components
        inputFileField = new JTextField(30);
        keyFileField = new JTextField(30);
        encryptedFileField = new JTextField(30);
        decryptedFileField = new JTextField(30);
        
        generateKeyButton = new JButton("Generate Key");
        encryptButton = new JButton("Encrypt File");
        decryptButton = new JButton("Decrypt File");
        browseInputButton = new JButton("Browse...");
        showKeyButton = new JButton("Show Key");
        downloadDecryptedButton = new JButton("Download Decrypted");
        
        keyDisplayArea = new JTextArea(6, 50);
        keyInputArea = new JTextArea(3, 30);
        previewTextArea = new JTextArea(10, 40);
        
        keyValidationLabel = new JLabel("Ready");
        statusLabel = new JLabel("Status: Ready");
        progressBar = new JProgressBar(0, 100);
        
        algorithmComboBox = new JComboBox<>(ALGORITHMS);
        keySizeComboBox = new JComboBox<>();
        
        imagePreviewLabel = new JLabel();
        imageScrollPane = new JScrollPane(imagePreviewLabel);

        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(new EmptyBorder(15, 15, 15, 15));

        // File selection panel
        JPanel filePanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.insets = new Insets(8, 8, 8, 8);

        // Input file row
        gbc.gridx = 0; gbc.gridy = 0;
        filePanel.add(new JLabel("Input File:"), gbc);
        
        gbc.gridx = 1; gbc.weightx = 1.0;
        filePanel.add(inputFileField, gbc);
        
        gbc.gridx = 2; gbc.weightx = 0.0;
        filePanel.add(browseInputButton, gbc);

        // Encrypted file row
        gbc.gridx = 0; gbc.gridy = 1;
        filePanel.add(new JLabel("Encrypted File:"), gbc);
        
        gbc.gridx = 1;
        filePanel.add(encryptedFileField, gbc);
        
        gbc.gridx = 2;
        JButton browseEncryptedButton = new JButton("Browse...");
        filePanel.add(browseEncryptedButton, gbc);

        // Decrypted file row
        gbc.gridx = 0; gbc.gridy = 2;
        filePanel.add(new JLabel("Decrypted File:"), gbc);
        
        gbc.gridx = 1;
        filePanel.add(decryptedFileField, gbc);
        
        gbc.gridx = 2;
        JButton browseDecryptedButton = new JButton("Browse...");
        filePanel.add(browseDecryptedButton, gbc);

        // Key file row
        gbc.gridx = 0; gbc.gridy = 3;
        filePanel.add(new JLabel("Key File:"), gbc);
        
        gbc.gridx = 1;
        filePanel.add(keyFileField, gbc);
        
        gbc.gridx = 2;
        JButton browseKeyButton = new JButton("Browse...");
        filePanel.add(browseKeyButton, gbc);

        // Algorithm and key size row
        gbc.gridx = 0; gbc.gridy = 4;
        filePanel.add(new JLabel("Algorithm:"), gbc);
        
        gbc.gridx = 1;
        filePanel.add(algorithmComboBox, gbc);
        
        gbc.gridx = 0; gbc.gridy = 5;
        filePanel.add(new JLabel("Key Size:"), gbc);
        
        gbc.gridx = 1;
        filePanel.add(keySizeComboBox, gbc);

        // Key input section for decryption
        gbc.gridx = 0; gbc.gridy = 6;
        filePanel.add(new JLabel("Decryption Key:"), gbc);
        
        gbc.gridx = 1; gbc.gridwidth = 2;
        keyInputArea.setToolTipText("Paste the Base64 encoded encryption key here for decryption");
        keyInputArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 11));
        keyInputArea.setBorder(BorderFactory.createLoweredBevelBorder());
        keyInputArea.setLineWrap(true);
        keyInputArea.setWrapStyleWord(false);
        JScrollPane keyInputScrollPane = new JScrollPane(keyInputArea);
        keyInputScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        filePanel.add(keyInputScrollPane, gbc);

        // Key validation status row
        gbc.gridx = 0; gbc.gridy = 7; gbc.gridwidth = 1;
        filePanel.add(new JLabel("Status:"), gbc);
        
        gbc.gridx = 1; gbc.gridwidth = 2;
        keyValidationLabel.setForeground(Color.GRAY);
        filePanel.add(keyValidationLabel, gbc);

        // Button panel
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 15, 0));
        generateKeyButton.setToolTipText("Generate a new encryption key");
        encryptButton.setToolTipText("Encrypt the selected file");
        decryptButton.setToolTipText("Decrypt using the entered key");
        decryptButton.setEnabled(false); // Initially disabled
        showKeyButton.setToolTipText("Show the current key");
        showKeyButton.setEnabled(false); // Initially disabled
        downloadDecryptedButton.setToolTipText("Download the decrypted file");
        downloadDecryptedButton.setEnabled(false); // Initially disabled

        buttonPanel.add(generateKeyButton);
        buttonPanel.add(encryptButton);
        buttonPanel.add(decryptButton);
        buttonPanel.add(showKeyButton);
        buttonPanel.add(downloadDecryptedButton);

        // Key display area
        keyDisplayArea.setEditable(false);
        keyDisplayArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 11));
        keyDisplayArea.setBackground(new Color(245, 245, 245));
        keyDisplayArea.setBorder(BorderFactory.createTitledBorder("Generated Key (Copy this for decryption)"));
        keyDisplayArea.setLineWrap(true);
        keyDisplayArea.setWrapStyleWord(false);
        JScrollPane keyScrollPane = new JScrollPane(keyDisplayArea);

        // Preview panel
        JPanel previewPanel = new JPanel(new BorderLayout(5, 5));
        previewPanel.setBorder(BorderFactory.createTitledBorder("File Preview"));
        
        previewTextArea.setEditable(false);
        previewTextArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane previewScrollPane = new JScrollPane(previewTextArea);
        
        imagePreviewLabel.setHorizontalAlignment(JLabel.CENTER);
        imageScrollPane.setPreferredSize(new Dimension(400, 300));
        imageScrollPane.setVisible(false); // Initially hidden
        
        previewPanel.add(previewScrollPane, BorderLayout.CENTER);
        previewPanel.add(imageScrollPane, BorderLayout.SOUTH);

        // Progress panel
        JPanel progressPanel = new JPanel(new BorderLayout(5, 0));
        progressBar.setStringPainted(true);
        progressBar.setString("Ready");
        progressPanel.add(progressBar, BorderLayout.CENTER);
        progressPanel.add(statusLabel, BorderLayout.EAST);

        // Tabs for different sections
        JTabbedPane tabbedPane = new JTabbedPane();
        
        JPanel fileTab = new JPanel(new BorderLayout(10, 10));
        fileTab.add(filePanel, BorderLayout.NORTH);
        fileTab.add(previewPanel, BorderLayout.CENTER);
        fileTab.add(progressPanel, BorderLayout.SOUTH);
        
        JPanel keyTab = new JPanel(new BorderLayout(10, 10));
        keyTab.add(keyScrollPane, BorderLayout.CENTER);
        
        tabbedPane.addTab("File Operations", fileTab);
        tabbedPane.addTab("Encryption Key", keyTab);

        // Add components to main panel
        mainPanel.add(tabbedPane, BorderLayout.CENTER);
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        // Set up the frame
        setContentPane(mainPanel);
        pack();
        setLocationRelativeTo(null);

        // Initialize key size options
        updateKeySizeOptions();

        // Action listeners
        generateKeyButton.addActionListener(this::handleGenerateKey);
        encryptButton.addActionListener(this::handleEncryptFile);
        decryptButton.addActionListener(this::handleDecryptFile);
        browseInputButton.addActionListener(e -> browseFile());
        browseEncryptedButton.addActionListener(e -> browseEncryptedFile());
        browseDecryptedButton.addActionListener(e -> browseDecryptedFile());
        browseKeyButton.addActionListener(e -> browseKeyFile());
        showKeyButton.addActionListener(e -> handleShowKey());
        downloadDecryptedButton.addActionListener(e -> handleDownloadDecrypted());
        
        algorithmComboBox.addActionListener(e -> {
            currentAlgorithm = (String) algorithmComboBox.getSelectedItem();
            updateKeySizeOptions();
        });
        
        keySizeComboBox.addActionListener(e -> {
            if (keySizeComboBox.getSelectedItem() != null) {
                currentKeySize = (Integer) keySizeComboBox.getSelectedItem();
            }
        });

        // Key input area listener for real-time validation
        keyInputArea.getDocument().addDocumentListener(new javax.swing.event.DocumentListener() {
            public void insertUpdate(javax.swing.event.DocumentEvent e) {
                SwingUtilities.invokeLater(() -> performKeyInputValidation());
            }
            public void removeUpdate(javax.swing.event.DocumentEvent e) {
                SwingUtilities.invokeLater(() -> performKeyInputValidation());
            }
            public void changedUpdate(javax.swing.event.DocumentEvent e) {
                SwingUtilities.invokeLater(() -> performKeyInputValidation());
            }
        });
        
        // Set default paths
        String userHome = System.getProperty("user.home");
        keyFileField.setText(userHome + File.separator + "encryption_key.key");
    }

    private void browseFile() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setCurrentDirectory(new File(System.getProperty("user.home")));
        
        // Set up file filters
        FileNameExtensionFilter allFilter = new FileNameExtensionFilter(
            "All Supported Files", "txt", "png", "jpg", "jpeg", "bmp", "gif", "pdf", "doc", "docx"
        );
        FileNameExtensionFilter imageFilter = new FileNameExtensionFilter(
            "Image Files", "png", "jpg", "jpeg", "bmp", "gif"
        );
        FileNameExtensionFilter textFilter = new FileNameExtensionFilter(
            "Text Files", "txt", "java", "html", "xml", "json", "csv", "md"
        );
        
        fileChooser.addChoosableFileFilter(allFilter);
        fileChooser.addChoosableFileFilter(imageFilter);
        fileChooser.addChoosableFileFilter(textFilter);
        fileChooser.setFileFilter(allFilter);
        
        int result = fileChooser.showOpenDialog(this);
        if (result == JFileChooser.APPROVE_OPTION) {
            File selectedFile = fileChooser.getSelectedFile();
            inputFileField.setText(selectedFile.getAbsolutePath());
            
            // Update preview for the selected file
            updatePreview(selectedFile.getAbsolutePath());
            
            // Generate default output filenames
            String inputPath = selectedFile.getAbsolutePath();
            encryptedFileField.setText(generateEncryptedFilename(inputPath));
            decryptedFileField.setText(generateDecryptedFilename(generateEncryptedFilename(inputPath)));
            
            // Reset key validation status
            updateKeyValidationStatus("Ready to encrypt/decrypt", Color.GRAY, false);
        }
    }

    private void browseEncryptedFile() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setCurrentDirectory(new File(System.getProperty("user.home")));
        fileChooser.setFileFilter(new FileNameExtensionFilter("Encrypted Files", "enc"));
        
        int result = fileChooser.showSaveDialog(this);
        if (result == JFileChooser.APPROVE_OPTION) {
            File selectedFile = fileChooser.getSelectedFile();
            String path = selectedFile.getAbsolutePath();
            if (!path.toLowerCase().endsWith(".enc")) {
                path += ".enc";
            }
            encryptedFileField.setText(path);
        }
    }

    private void browseDecryptedFile() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setCurrentDirectory(new File(System.getProperty("user.home")));
        
        int result = fileChooser.showSaveDialog(this);
        if (result == JFileChooser.APPROVE_OPTION) {
            decryptedFileField.setText(fileChooser.getSelectedFile().getAbsolutePath());
        }
    }

    private void browseKeyFile() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setCurrentDirectory(new File(System.getProperty("user.home")));
        fileChooser.setFileFilter(new FileNameExtensionFilter("Key Files", "key"));
        
        int result = fileChooser.showSaveDialog(this);
        if (result == JFileChooser.APPROVE_OPTION) {
            File selectedFile = fileChooser.getSelectedFile();
            String path = selectedFile.getAbsolutePath();
            if (!path.toLowerCase().endsWith(".key")) {
                path += ".key";
            }
            keyFileField.setText(path);
        }
    }

    private void updateKeySizeOptions() {
        keySizeComboBox.removeAllItems();
        switch (currentAlgorithm) {
            case "AES":
                keySizeComboBox.addItem(128);
                keySizeComboBox.addItem(192);
                keySizeComboBox.addItem(256);
                break;
            case "DES":
                keySizeComboBox.addItem(56);
                break;
            case "Blowfish":
                keySizeComboBox.addItem(32);
                keySizeComboBox.addItem(64);
                keySizeComboBox.addItem(128);
                keySizeComboBox.addItem(256);
                keySizeComboBox.addItem(448);
                break;
        }
        if (keySizeComboBox.getItemCount() > 0) {
            keySizeComboBox.setSelectedIndex(0);
            currentKeySize = (Integer) keySizeComboBox.getSelectedItem();
        }
    }

    private void toggleDarkMode(boolean darkMode) {
        try {
            if (darkMode) {
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
                SwingUtilities.updateComponentTreeUI(this);
                previewTextArea.setBackground(Color.DARK_GRAY);
                previewTextArea.setForeground(Color.WHITE);
                statusLabel.setText("Status: Dark Mode Enabled");
            } else {
                UIManager.setLookAndFeel(UIManager.getCrossPlatformLookAndFeelClassName());
                SwingUtilities.updateComponentTreeUI(this);
                previewTextArea.setBackground(Color.WHITE);
                previewTextArea.setForeground(Color.BLACK);
                statusLabel.setText("Status: Light Mode Enabled");
            }
        } catch (Exception e) {
            showError(e);
        }
    }

    private void updatePreview(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists() || !file.isFile() || !file.canRead()) {
                previewTextArea.setText("File not found or cannot be read: " + filePath);
                imagePreviewLabel.setIcon(null);
                imageScrollPane.setVisible(false);
                return;
            }
            
            String fileType = getFileType(file.getName());
            
            // Reset preview components
            imagePreviewLabel.setIcon(null);
            imageScrollPane.setVisible(false);
            
            if (fileType.equals("Image")) {
                try {
                    // Load and display the image
                    ImageIcon originalIcon = new ImageIcon(filePath);
                    
                    // Check if it's a valid image
                    if (originalIcon.getIconWidth() <= 0) {
                        showFileMetadata(file, fileType);
                        return;
                    }
                    
                    // Get dimensions
                    int width = originalIcon.getIconWidth();
                    int height = originalIcon.getIconHeight();
                    
                    // Calculate scaling
                    int maxHeight = 300;
                    int maxWidth = 400;
                    double scale = 1.0;
                    
                    if (height > maxHeight) {
                        scale = (double)maxHeight / height;
                    }
                    if (width * scale > maxWidth) {
                        scale = (double)maxWidth / width;
                    }
                    
                    // Scale the image
                    int scaledWidth = (int)(width * scale);
                    int scaledHeight = (int)(height * scale);
                    
                    Image scaledImage = originalIcon.getImage().getScaledInstance(
                        scaledWidth, scaledHeight, Image.SCALE_SMOOTH);
                    
                    // Display the image
                    imagePreviewLabel.setIcon(new ImageIcon(scaledImage));
                    
                    // Show image info in text area
                    StringBuilder info = new StringBuilder();
                    info.append("[Image file] ").append(file.getName()).append("\n");
                    info.append("Size: ").append(formatFileSize(file.length())).append("\n");
                    info.append("Dimensions: ").append(width).append(" x ")
                        .append(height).append(" pixels\n");
                    info.append("Last modified: ").append(new java.util.Date(file.lastModified()));
                    
                    previewTextArea.setText(info.toString());
                    
                    // Make image preview visible
                    imageScrollPane.setVisible(true);
                    
                } catch (Exception ex) {
                    // If we can't display the image, show metadata
                    showFileMetadata(file, fileType);
                }
            } else if (fileType.equals("Text")) {
                // For text files, show content preview
                try {
                    String content = new String(Files.readAllBytes(Paths.get(filePath)));
                    if (content.length() > 1000) {
                        content = content.substring(0, 1000) + "... (file truncated)";
                    }
                    previewTextArea.setText(content);
                } catch (Exception ex) {
                    showFileMetadata(file, fileType);
                }
            } else {
                // For other binary files, show metadata
                showFileMetadata(file, fileType);
            }
            
            statusLabel.setText("Status: File preview loaded");
            
        } catch (Exception e) {
            previewTextArea.setText("Error reading file: " + e.getMessage());
            imagePreviewLabel.setIcon(null);
            imageScrollPane.setVisible(false);
        }
    }

    private void showFileMetadata(File file, String fileType) {
        StringBuilder info = new StringBuilder();
        info.append("[").append(fileType).append(" file] ").append(file.getName()).append("\n");
        info.append("Size: ").append(formatFileSize(file.length())).append("\n");
        info.append("Last modified: ").append(new java.util.Date(file.lastModified())).append("\n");
        previewTextArea.setText(info.toString());
    }

    private void updateProgress(int value, String message) {
        progressBar.setValue(value);
        progressBar.setString(message);
        statusLabel.setText("Status: " + message);
    }

    private void handleShowKey() {
        try {
            String keyPath = keyFileField.getText().trim();
            if (keyPath.isEmpty()) {
                JOptionPane.showMessageDialog(this,
                    "Please specify a key file path first.",
                    "No Key File", JOptionPane.WARNING_MESSAGE);
                return;
            }

            File keyFile = new File(keyPath);
            if (!keyFile.exists()) {
                JOptionPane.showMessageDialog(this,
                    "Key file does not exist: " + keyPath,
                    "Key File Not Found", JOptionPane.ERROR_MESSAGE);
                return;
            }

            // Read and display the key
            byte[] keyBytes = Files.readAllBytes(Paths.get(keyPath));
            String keyContent = new String(keyBytes);

            // Display in the key tab
            keyDisplayArea.setText("Key File: " + keyFile.getName() + "\n" +
                                 "Key Size: " + keyBytes.length + " bytes\n" +
                                 "Algorithm: " + currentAlgorithm + "\n\n" +
                                 "Key Content (Base64):\n" + keyContent);

            // Also show in a popup for easy copying
            JTextArea popupArea = new JTextArea(10, 50);
            popupArea.setText(keyContent);
            popupArea.setEditable(false);
            popupArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
            popupArea.setCaretPosition(0);

            JScrollPane scrollPane = new JScrollPane(popupArea);

            JPanel panel = new JPanel(new BorderLayout());
            panel.add(new JLabel("Encryption Key (Base64 encoded):"), BorderLayout.NORTH);
            panel.add(scrollPane, BorderLayout.CENTER);

            JButton copyButton = new JButton("Copy to Clipboard");
            copyButton.addActionListener(e -> {
                java.awt.datatransfer.StringSelection stringSelection =
                    new java.awt.datatransfer.StringSelection(keyContent);
                java.awt.Toolkit.getDefaultToolkit().getSystemClipboard()
                    .setContents(stringSelection, null);
                JOptionPane.showMessageDialog(this, "Key copied to clipboard!");
            });

            JPanel buttonPanel = new JPanel();
            buttonPanel.add(copyButton);
            panel.add(buttonPanel, BorderLayout.SOUTH);

            JOptionPane.showMessageDialog(this, panel, "Encryption Key", JOptionPane.INFORMATION_MESSAGE);

        } catch (Exception e) {
            showError(e);
        }
    }

    private void handleValidateKey() {
        performKeyValidation();
    }

    private void performKeyValidation() {
        try {
            String keyPath = keyFileField.getText().trim();

            if (keyPath.isEmpty()) {
                updateKeyValidationStatus("No key file specified", Color.GRAY, false);
                return;
            }

            File keyFile = new File(keyPath);

            if (!keyFile.exists()) {
                updateKeyValidationStatus("Key file not found", Color.RED, false);
                return;
            }

            // Validate key by attempting to read it
            try {
                SecretKey key = loadKey(keyPath, currentAlgorithm);
                updateKeyValidationStatus("✓ Key file is valid", Color.GREEN, true);

            } catch (Exception e) {
                updateKeyValidationStatus("✗ Invalid key file format", Color.RED, false);
            }

        } catch (Exception e) {
            updateKeyValidationStatus("Validation error", Color.RED, false);
        }
    }

    private void performKeyInputValidation() {
        try {
            String keyInput = keyInputArea.getText().trim();
            String inputFilePath = inputFileField.getText().trim();

            if (keyInput.isEmpty()) {
                updateKeyValidationStatus("No key entered", Color.GRAY, false);
                decryptButton.setEnabled(false);
                return;
            }

            if (inputFilePath.isEmpty()) {
                updateKeyValidationStatus("No input file selected", Color.GRAY, false);
                decryptButton.setEnabled(false);
                return;
            }

            File inputFile = new File(inputFilePath);
            if (!inputFile.exists()) {
                updateKeyValidationStatus("Input file not found", Color.RED, false);
                decryptButton.setEnabled(false);
                return;
            }

            // Validate key format (Base64)
            try {
                byte[] keyBytes = java.util.Base64.getDecoder().decode(keyInput);
                SecretKey key = new javax.crypto.spec.SecretKeySpec(keyBytes, currentAlgorithm);

                // Try to validate the key by attempting a small decryption test
                boolean isValid = validateKeyForFile(key, inputFilePath);

                if (isValid) {
                    updateKeyValidationStatus("✓ Key is valid for decryption", Color.GREEN, true);
                    decryptButton.setEnabled(true);
                    downloadDecryptedButton.setEnabled(false); // Will be enabled after successful decryption
                } else {
                    updateKeyValidationStatus("✗ Key does not match the encrypted file", Color.RED, false);
                    decryptButton.setEnabled(false);
                }

            } catch (IllegalArgumentException e) {
                updateKeyValidationStatus("✗ Invalid Base64 key format", Color.RED, false);
                decryptButton.setEnabled(false);
            } catch (Exception e) {
                updateKeyValidationStatus("✗ Invalid key", Color.RED, false);
                decryptButton.setEnabled(false);
            }

        } catch (Exception e) {
            updateKeyValidationStatus("Validation error", Color.RED, false);
            decryptButton.setEnabled(false);
        }
    }

    private boolean validateKeyForFile(SecretKey key, String encryptedFilePath) {
        try {
            // Create a temporary file for validation
            File tempFile = File.createTempFile("validation", ".tmp");
            tempFile.deleteOnExit();

            // Try to decrypt just the first few bytes
            Cipher cipher = Cipher.getInstance(currentAlgorithm);
            cipher.init(Cipher.DECRYPT_MODE, key);

            try (FileInputStream fis = new FileInputStream(encryptedFilePath)) {
                byte[] buffer = new byte[Math.min(1024, fis.available())]; // Read first 1KB or less
                int bytesRead = fis.read(buffer);

                if (bytesRead > 0) {
                    // Try to decrypt the sample
                    cipher.update(buffer, 0, bytesRead);
                    return true; // If no exception, key is likely valid
                }
            }

            return false;
        } catch (Exception e) {
            return false; // If any exception occurs, key is invalid
        }
    }

    private void updateKeyValidationStatus(String message, Color color, boolean enableShowKey) {
        keyValidationLabel.setText(message);
        keyValidationLabel.setForeground(color);
        showKeyButton.setEnabled(enableShowKey || new File(keyFileField.getText().trim()).exists());
    }

    private void handleGenerateKey(ActionEvent e) {
        try {
            updateProgress(0, "Generating key...");
            SecretKey key = generateKey(currentAlgorithm, currentKeySize);
            String keyPath = keyFileField.getText();
            saveKey(key, keyPath);

            // Store the key content for display
            byte[] keyBytes = Files.readAllBytes(Paths.get(keyPath));
            lastGeneratedKey = new String(keyBytes);

            // Display the key in the key tab
            keyDisplayArea.setText("Key File: " + new File(keyPath).getName() + "\n" +
                                 "Key Size: " + keyBytes.length + " bytes\n" +
                                 "Algorithm: " + currentAlgorithm + "\n" +
                                 "Key Strength: " + currentKeySize + " bits\n\n" +
                                 "Key Content (Base64):\n" + lastGeneratedKey);

            updateProgress(100, "Key generated successfully");

            // Enable show key button
            showKeyButton.setEnabled(true);
            updateKeyValidationStatus("New key generated", Color.BLUE, true);

            // Show key in popup after generation
            int choice = JOptionPane.showConfirmDialog(this,
                "Key generated successfully!\n\nWould you like to view the key now?",
                "Key Generated",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.INFORMATION_MESSAGE);

            if (choice == JOptionPane.YES_OPTION) {
                handleShowKey();
            }

        } catch (Exception ex) {
            updateProgress(0, "Error: " + ex.getMessage());
            showError(ex);
        }
    }

    private void handleEncryptFile(ActionEvent e) {
        try {
            // Validate input file first
            File inputFile = new File(inputFileField.getText());
            if (!validateInputFile(inputFile)) {
                JOptionPane.showMessageDialog(this,
                    "Please select a valid input file (text or image).",
                    "Invalid Input File", JOptionPane.WARNING_MESSAGE);
                return;
            }

            updateProgress(0, "Loading key...");
            SecretKey key = loadKey(keyFileField.getText(), currentAlgorithm);
            updateProgress(10, "Encrypting file...");

            // Generate output filename for encrypted file
            String outputPath = generateEncryptedFilename(inputFileField.getText());

            // Use instance method instead of static method
            encryptFile(inputFileField.getText(), outputPath, key, currentAlgorithm);

            updateProgress(100, "File encrypted successfully");

            // Display the encryption key after successful encryption
            try {
                byte[] keyBytes = Files.readAllBytes(Paths.get(keyFileField.getText()));
                String keyContent = new String(keyBytes);

                keyDisplayArea.setText("Encryption completed!\n\n" +
                                     "Input File: " + inputFile.getName() + "\n" +
                                     "Encrypted File: " + new File(outputPath).getName() + "\n" +
                                     "Algorithm: " + currentAlgorithm + "\n" +
                                     "Key Size: " + currentKeySize + " bits\n\n" +
                                     "Key Content (Base64):\n" + keyContent);

                // Enable show key button
                showKeyButton.setEnabled(true);

                // Perform validation to enable decrypt button if key matches
                performKeyValidation();

            } catch (Exception keyEx) {
                System.out.println("Could not display key: " + keyEx.getMessage());
            }

            // Show success message with option to view key
            String fileType = getFileType(inputFileField.getText());
            String message = "File encrypted successfully!\nEncrypted file: " + outputPath;

            if (fileType.equals("Image")) {
                message += "\n\nThe encryption key is now displayed in the 'Encryption Key' tab.";

                int choice = JOptionPane.showConfirmDialog(this,
                    message + "\n\nWould you like to view the encryption key?",
                    "Encryption Complete",
                    JOptionPane.YES_NO_OPTION,
                    JOptionPane.INFORMATION_MESSAGE);

                if (choice == JOptionPane.YES_OPTION) {
                    handleShowKey();
                }
            } else {
                JOptionPane.showMessageDialog(this, message);
            }

            // Update the input field to point to the encrypted file for decryption
            inputFileField.setText(outputPath);
            updatePreview(outputPath);

        } catch (Exception ex) {
            updateProgress(0, "Error: " + ex.getMessage());
            showError(ex);
        }
    }

    private String generateEncryptedFilename(String inputPath) {
        File inputFile = new File(inputPath);
        String inputName = inputFile.getName();
        String inputExtension = "";

        int lastDot = inputName.lastIndexOf('.');
        if (lastDot > 0) {
            inputExtension = inputName.substring(lastDot);
        }

        String baseName = lastDot > 0 ? inputName.substring(0, lastDot) : inputName;
        return baseName + "_encrypted" + inputExtension + ".enc";
    }

    private String generateDecryptedFilename(String encryptedPath) {
        File encryptedFile = new File(encryptedPath);
        String encryptedName = encryptedFile.getName();
        
        // Remove .enc extension if present
        if (encryptedName.toLowerCase().endsWith(".enc")) {
            encryptedName = encryptedName.substring(0, encryptedName.length() - 4);
        }
        
        // Remove _encrypted suffix if present
        if (encryptedName.contains("_encrypted")) {
            encryptedName = encryptedName.replace("_encrypted", "_decrypted");
        } else {
            // If no _encrypted suffix, add _decrypted before the extension
            int lastDot = encryptedName.lastIndexOf('.');
            if (lastDot > 0) {
                encryptedName = encryptedName.substring(0, lastDot) + 
                               "_decrypted" + encryptedName.substring(lastDot);
            } else {
                encryptedName = encryptedName + "_decrypted";
            }
        }
        
        return new File(encryptedFile.getParent(), encryptedName).getAbsolutePath();
    }

    private void handleDecryptFile(ActionEvent e) {
        try {
            // Validate input file (encrypted file) exists
            String inputFilePath = inputFileField.getText().trim();
            File inputFile = new File(inputFilePath);
            if (!inputFile.exists()) {
                JOptionPane.showMessageDialog(this,
                    "Input file does not exist: " + inputFilePath,
                    "File Not Found", JOptionPane.ERROR_MESSAGE);
                return;
            }

            // Validate key input
            String keyInput = keyInputArea.getText().trim();
            if (keyInput.isEmpty()) {
                JOptionPane.showMessageDialog(this,
                    "Please enter the decryption key in the key input area.",
                    "No Key Provided", JOptionPane.WARNING_MESSAGE);
                return;
            }

            // Validate key before attempting decryption
            updateProgress(0, "Validating key...");
            SecretKey key;
            try {
                // Decode the Base64 key
                byte[] keyBytes = java.util.Base64.getDecoder().decode(keyInput);
                key = new javax.crypto.spec.SecretKeySpec(keyBytes, currentAlgorithm);

                // Validate that the key matches the encrypted file
                if (!validateKeyForFile(key, inputFilePath)) {
                    JOptionPane.showMessageDialog(this,
                        "The entered key does not match the encrypted file.\n" +
                        "Please verify you have the correct key.",
                        "Key Validation Failed", JOptionPane.ERROR_MESSAGE);
                    updateKeyValidationStatus("✗ Key does not match encrypted file", Color.RED, false);
                    return;
                }

                updateKeyValidationStatus("✓ Key validated successfully", Color.GREEN, true);

            } catch (IllegalArgumentException keyEx) {
                JOptionPane.showMessageDialog(this,
                    "Invalid key format. Please ensure the key is properly Base64 encoded.",
                    "Key Format Error", JOptionPane.ERROR_MESSAGE);
                updateKeyValidationStatus("✗ Invalid Base64 key format", Color.RED, false);
                return;
            } catch (Exception keyEx) {
                JOptionPane.showMessageDialog(this,
                    "Invalid key:\n" + keyEx.getMessage(),
                    "Key Error", JOptionPane.ERROR_MESSAGE);
                updateKeyValidationStatus("✗ Invalid key", Color.RED, false);
                return;
            }

            updateProgress(20, "Decrypting file...");

            // Generate output filename
            String outputPath = generateDecryptedFilename(inputFilePath);
            decryptedFileField.setText(outputPath);

            decryptFile(inputFilePath, outputPath, key, currentAlgorithm);

            updateProgress(100, "File decrypted successfully");

            // Enable download button for decrypted file
            downloadDecryptedButton.setEnabled(true);

            JOptionPane.showMessageDialog(this,
                "File decrypted successfully!\nDecrypted file: " + outputPath);

            // Update the input field to point to the decrypted file
            inputFileField.setText(outputPath);
            updatePreview(outputPath);

            // Clear the key input area for security
            keyInputArea.setText("");
            updateKeyValidationStatus("Decryption completed", Color.BLUE, false);

        } catch (Exception ex) {
            updateProgress(0, "Error: " + ex.getMessage());
            showError(ex);
        }
    }

    private void showError(Exception e) {
        String errorMessage = e.getMessage();
        String errorTitle = "Error";

        // Provide more specific error messages
        if (e instanceof FileNotFoundException) {
            errorTitle = "File Not Found";
            errorMessage = "The specified file could not be found:\n" + errorMessage;
        } else if (e instanceof IOException) {
            errorTitle = "File I/O Error";
            if (errorMessage.contains("trailing char")) {
                errorMessage = "Invalid file path. Please check the file path and try again.";
            } else if (errorMessage.contains("key")) {
                errorMessage = "Key file error: " + errorMessage;
            } else {
                errorMessage = "File operation failed: " + errorMessage;
            }
        } else if (e instanceof IllegalArgumentException) {
            errorTitle = "Invalid Input";
            errorMessage = "Invalid input provided: " + errorMessage;
        } else if (e instanceof SecurityException) {
            errorTitle = "Security Error";
            errorMessage = "Access denied: " + errorMessage;
        }

        JOptionPane.showMessageDialog(this, errorMessage, errorTitle, JOptionPane.ERROR_MESSAGE);
        e.printStackTrace();
    }

    public static SecretKey generateKey(String algorithm, int keySize) throws Exception {
        KeyGenerator keyGen = KeyGenerator.getInstance(algorithm);
        keyGen.init(keySize, new SecureRandom());
        return keyGen.generateKey();
    }

    public static void saveKey(SecretKey key, String filePath) throws IOException {
        // Clean the file path to remove any trailing characters
        filePath = filePath.trim().replaceAll("[<>]", "");

        try {
            byte[] encodedKey = key.getEncoded();
            Files.write(Paths.get(filePath), Base64.getEncoder().encode(encodedKey));
        } catch (Exception e) {
            throw new IOException("Failed to save key to file: " + filePath, e);
        }
    }

    public static SecretKey loadKey(String filePath, String algorithm) throws IOException {
        // Clean the file path to remove any trailing characters
        filePath = filePath.trim().replaceAll("[<>]", "");

        File keyFile = new File(filePath);
        if (!keyFile.exists()) {
            throw new IOException("Key file does not exist: " + filePath);
        }

        if (!keyFile.canRead()) {
            throw new IOException("Cannot read key file: " + filePath);
        }

        try {
            byte[] encodedKey = Base64.getDecoder().decode(Files.readAllBytes(Paths.get(filePath)));
            return new SecretKeySpec(encodedKey, algorithm);
        } catch (IllegalArgumentException e) {
            throw new IOException("Invalid key file format. The key file may be corrupted or not a valid Base64 encoded key.", e);
        }
    }

    // Update buffer size for better memory efficiency
    private static final int BUFFER_SIZE = 8192; // Increased from 4096 for better performance

    // Add file type detection
    private static final String[] IMAGE_EXTENSIONS = {".png", ".jpg", ".jpeg", ".gif", ".bmp"};
    private static final String[] DOCUMENT_EXTENSIONS = {".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx"};
    private static final String[] ARCHIVE_EXTENSIONS = {".zip", ".rar", ".7z", ".tar", ".gz"};

    // Improved file type detection
    private String getFileType(String fileName) {
        fileName = fileName.toLowerCase();
        
        for (String ext : IMAGE_EXTENSIONS) {
            if (fileName.endsWith(ext)) return "Image";
        }
        
        for (String ext : DOCUMENT_EXTENSIONS) {
            if (fileName.endsWith(ext)) return "Document";
        }
        
        for (String ext : ARCHIVE_EXTENSIONS) {
            if (fileName.endsWith(ext)) return "Archive";
        }
        
        if (isTextFile(fileName)) return "Text";
        
        return "Binary";
    }

    // Improved encryption with memory usage reporting
    public void encryptFile(String inputFile, String outputFile, SecretKey key, String algorithm) throws Exception {
        Cipher cipher = Cipher.getInstance(algorithm);
        cipher.init(Cipher.ENCRYPT_MODE, key);
        
        File inFile = new File(inputFile);
        long fileSize = inFile.length();
        long processedBytes = 0;
        
        try (FileInputStream fis = new FileInputStream(inputFile);
             FileOutputStream fos = new FileOutputStream(outputFile)) {
            
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            
            while ((bytesRead = fis.read(buffer)) != -1) {
                byte[] outputBytes = cipher.update(buffer, 0, bytesRead);
                if (outputBytes != null) {
                    fos.write(outputBytes);
                }
                
                // Update progress
                processedBytes += bytesRead;
                int progressPercent = (int)((processedBytes * 100) / fileSize);
                updateProgress(progressPercent, "Encrypting: " + progressPercent + "%");
            }
            
            byte[] outputBytes = cipher.doFinal();
            if (outputBytes != null) {
                fos.write(outputBytes);
            }
            
            // Log memory usage
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = runtime.totalMemory() - runtime.freeMemory();
            System.out.println("Memory used: " + (usedMemory / 1024 / 1024) + " MB");
        }
    }

    // Improved decryption with memory usage reporting
    public void decryptFile(String inputFile, String outputFile, SecretKey key, String algorithm) throws Exception {
        Cipher cipher = Cipher.getInstance(algorithm);
        cipher.init(Cipher.DECRYPT_MODE, key);
        
        File inFile = new File(inputFile);
        long fileSize = inFile.length();
        long processedBytes = 0;
        
        try (FileInputStream fis = new FileInputStream(inputFile);
             FileOutputStream fos = new FileOutputStream(outputFile)) {
            
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            
            while ((bytesRead = fis.read(buffer)) != -1) {
                byte[] outputBytes = cipher.update(buffer, 0, bytesRead);
                if (outputBytes != null) {
                    fos.write(outputBytes);
                }
                
                // Update progress
                processedBytes += bytesRead;
                int progressPercent = (int)((processedBytes * 100) / fileSize);
                updateProgress(progressPercent, "Decrypting: " + progressPercent + "%");
            }
            
            byte[] outputBytes = cipher.doFinal();
            if (outputBytes != null) {
                fos.write(outputBytes);
            }
            
            // Log memory usage
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = runtime.totalMemory() - runtime.freeMemory();
            System.out.println("Memory used: " + (usedMemory / 1024 / 1024) + " MB");
        }
    }

    private boolean isTextFile(String fileName) {
        String[] textExtensions = {".txt", ".java", ".html", ".xml", ".json", ".csv", ".md"};
        for (String ext : textExtensions) {
            if (fileName.endsWith(ext)) return true;
        }
        return false;
    }

    // Helper method to format file size
    private String formatFileSize(long size) {
        final String[] units = new String[] { "B", "KB", "MB", "GB", "TB" };
        int unitIndex = 0;
        double sizeAsDouble = size;
        
        while (sizeAsDouble >= 1024 && unitIndex < units.length - 1) {
            sizeAsDouble /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", sizeAsDouble, units[unitIndex]);
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            FileEncryptorGUI app = new FileEncryptorGUI();
            app.setVisible(true);
        });
    }
}
