import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.SecureRandom;
import java.util.Base64;

public class FileEncryptorGUI extends JFrame {

    private static final String[] ALGORITHMS = {"AES", "DES", "Blowfish"};
    private static final int[] KEY_SIZES = {128, 192, 256};

    private String currentAlgorithm = "AES";
    private int currentKeySize = 256;

    private JTextField inputFileField, encryptedFileField, decryptedFileField, keyFileField;
    private JButton generateKeyButton, encryptButton, decryptButton;
    private JButton browseInputButton, browseEncryptedButton, browseDecryptedButton, browseKeyButton;
    private JComboBox<String> algorithmComboBox;
    private JComboBox<Integer> keySizeComboBox;
    private JProgressBar progressBar;
    private JTextArea previewTextArea;
    private JToggleButton darkModeToggle;
    private JLabel statusLabel;
    private JLabel imagePreviewLabel;
    private JScrollPane imageScrollPane;
    private JPanel previewPanel;

    public FileEncryptorGUI() {
        setTitle("File Encryptor");
        setSize(800, 600);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);

        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(new EmptyBorder(10, 10, 10, 10));

        JPanel settingsPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        algorithmComboBox = new JComboBox<>(ALGORITHMS);
        algorithmComboBox.setSelectedItem(currentAlgorithm);

        keySizeComboBox = new JComboBox<>();
        for (int size : KEY_SIZES) keySizeComboBox.addItem(size);
        keySizeComboBox.setSelectedItem(currentKeySize);

        darkModeToggle = new JToggleButton("Dark Mode");

        settingsPanel.add(new JLabel("Algorithm:"));
        settingsPanel.add(algorithmComboBox);
        settingsPanel.add(new JLabel("Key Size:"));
        settingsPanel.add(keySizeComboBox);
        settingsPanel.add(darkModeToggle);

        JPanel filePanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.insets = new Insets(5, 5, 5, 5);

        gbc.gridx = 0; gbc.gridy = 0;
        filePanel.add(new JLabel("Input File:"), gbc);
        gbc.gridx = 1; gbc.weightx = 1.0;
        inputFileField = new JTextField("input.txt", 20);
        filePanel.add(inputFileField, gbc);
        gbc.gridx = 2; gbc.weightx = 0.0;
        browseInputButton = new JButton("Browse...");
        filePanel.add(browseInputButton, gbc);

        gbc.gridx = 0; gbc.gridy = 1;
        filePanel.add(new JLabel("Encrypted File:"), gbc);
        gbc.gridx = 1;
        encryptedFileField = new JTextField("encrypt.txt", 20);
        filePanel.add(encryptedFileField, gbc);
        gbc.gridx = 2;
        browseEncryptedButton = new JButton("Browse...");
        filePanel.add(browseEncryptedButton, gbc);

        gbc.gridx = 0; gbc.gridy = 2;
        filePanel.add(new JLabel("Decrypted File:"), gbc);
        gbc.gridx = 1;
        decryptedFileField = new JTextField("decryp.txt", 20);
        filePanel.add(decryptedFileField, gbc);
        gbc.gridx = 2;
        browseDecryptedButton = new JButton("Browse...");
        filePanel.add(browseDecryptedButton, gbc);

        gbc.gridx = 0; gbc.gridy = 3;
        filePanel.add(new JLabel("Key File:"), gbc);
        gbc.gridx = 1;
        keyFileField = new JTextField("secret.key", 20);
        filePanel.add(keyFileField, gbc);
        gbc.gridx = 2;
        browseKeyButton = new JButton("Browse...");
        filePanel.add(browseKeyButton, gbc);

        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 0));
        generateKeyButton = new JButton("Generate Key");
        encryptButton = new JButton("Encrypt File");
        decryptButton = new JButton("Decrypt File");
        buttonPanel.add(generateKeyButton);
        buttonPanel.add(encryptButton);
        buttonPanel.add(decryptButton);

        previewPanel = new JPanel(new BorderLayout());
        previewPanel.setBorder(BorderFactory.createTitledBorder("File Preview"));
        
        // Text preview
        previewTextArea = new JTextArea(10, 40);
        previewTextArea.setEditable(false);
        JScrollPane textScrollPane = new JScrollPane(previewTextArea);
        
        // Image preview
        imagePreviewLabel = new JLabel();
        imagePreviewLabel.setHorizontalAlignment(JLabel.CENTER);
        imageScrollPane = new JScrollPane(imagePreviewLabel);
        imageScrollPane.setVisible(false);
        
        // Add both to the preview panel with card layout
        previewPanel.add(textScrollPane, BorderLayout.CENTER);
        previewPanel.add(imageScrollPane, BorderLayout.SOUTH);

        JPanel progressPanel = new JPanel(new BorderLayout(5, 5));
        progressBar = new JProgressBar(0, 100);
        progressBar.setStringPainted(true);
        progressBar.setString("Ready");
        statusLabel = new JLabel("Status: Ready");
        progressPanel.add(progressBar, BorderLayout.CENTER);
        progressPanel.add(statusLabel, BorderLayout.SOUTH);

        mainPanel.add(settingsPanel, BorderLayout.NORTH);
        mainPanel.add(filePanel, BorderLayout.CENTER);
        JPanel southPanel = new JPanel(new BorderLayout());
        southPanel.add(buttonPanel, BorderLayout.NORTH);
        southPanel.add(previewPanel, BorderLayout.CENTER);
        southPanel.add(progressPanel, BorderLayout.SOUTH);
        mainPanel.add(southPanel, BorderLayout.SOUTH);

        add(mainPanel);

        generateKeyButton.addActionListener(this::handleGenerateKey);
        encryptButton.addActionListener(this::handleEncryptFile);
        decryptButton.addActionListener(this::handleDecryptFile);

        browseInputButton.addActionListener(e -> browseFile(inputFileField));
        browseEncryptedButton.addActionListener(e -> browseFile(encryptedFileField));
        browseDecryptedButton.addActionListener(e -> browseFile(decryptedFileField));
        browseKeyButton.addActionListener(e -> browseFile(keyFileField));

        algorithmComboBox.addActionListener(e -> {
            currentAlgorithm = (String) algorithmComboBox.getSelectedItem();
            updateKeySizeOptions();
        });

        keySizeComboBox.addActionListener(e -> {
            if (keySizeComboBox.getSelectedItem() != null) {
                currentKeySize = (Integer) keySizeComboBox.getSelectedItem();
            }
        });

        darkModeToggle.addActionListener(e -> toggleDarkMode(darkModeToggle.isSelected()));
        inputFileField.addActionListener(e -> updatePreview(inputFileField.getText()));
    }

    private void browseFile(JTextField textField) {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setCurrentDirectory(new File(System.getProperty("user.home")));
        FileNameExtensionFilter filter = new FileNameExtensionFilter(
            "Supported Files (.txt, .png, .jpg, .jpeg, .bmp, .gif)",
            "txt", "png", "jpg", "jpeg", "bmp", "gif"
        );
        fileChooser.setFileFilter(filter);
        fileChooser.setAcceptAllFileFilterUsed(true);

        if (fileChooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            textField.setText(fileChooser.getSelectedFile().getAbsolutePath());
            if (textField == inputFileField) {
                updatePreview(textField.getText());
            }
        }
    }

    private void updateKeySizeOptions() {
        keySizeComboBox.removeAllItems();
        switch (currentAlgorithm) {
            case "AES":
                keySizeComboBox.addItem(128);
                keySizeComboBox.addItem(192);
                keySizeComboBox.addItem(256);
                break;
            case "DES":
                keySizeComboBox.addItem(56);
                break;
            case "Blowfish":
                keySizeComboBox.addItem(32);
                keySizeComboBox.addItem(64);
                keySizeComboBox.addItem(128);
                keySizeComboBox.addItem(256);
                keySizeComboBox.addItem(448);
                break;
        }
        if (keySizeComboBox.getItemCount() > 0) {
            keySizeComboBox.setSelectedIndex(0);
            currentKeySize = (Integer) keySizeComboBox.getSelectedItem();
        }
    }

    private void toggleDarkMode(boolean darkMode) {
        try {
            if (darkMode) {
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
                SwingUtilities.updateComponentTreeUI(this);
                previewTextArea.setBackground(Color.DARK_GRAY);
                previewTextArea.setForeground(Color.WHITE);
                statusLabel.setText("Status: Dark Mode Enabled");
            } else {
                UIManager.setLookAndFeel(UIManager.getCrossPlatformLookAndFeelClassName());
                SwingUtilities.updateComponentTreeUI(this);
                previewTextArea.setBackground(Color.WHITE);
                previewTextArea.setForeground(Color.BLACK);
                statusLabel.setText("Status: Light Mode Enabled");
            }
        } catch (Exception e) {
            showError(e);
        }
    }

    private void updatePreview(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists() || !file.isFile() || !file.canRead()) {
                previewTextArea.setText("File not found or cannot be read: " + filePath);
                imagePreviewLabel.setIcon(null);
                imageScrollPane.setVisible(false);
                return;
            }
            
            String fileType = getFileType(file.getName());
            
            // Reset preview components
            imagePreviewLabel.setIcon(null);
            imageScrollPane.setVisible(false);
            
            if (fileType.equals("Image")) {
                try {
                    // Load and display the image
                    ImageIcon originalIcon = new ImageIcon(filePath);
                    
                    // Check if it's a valid image
                    if (originalIcon.getIconWidth() <= 0) {
                        showFileMetadata(file, fileType);
                        return;
                    }
                    
                    // Get dimensions
                    int width = originalIcon.getIconWidth();
                    int height = originalIcon.getIconHeight();
                    
                    // Calculate scaling
                    int maxHeight = 300;
                    int maxWidth = 400;
                    double scale = 1.0;
                    
                    if (height > maxHeight) {
                        scale = (double)maxHeight / height;
                    }
                    if (width * scale > maxWidth) {
                        scale = (double)maxWidth / width;
                    }
                    
                    // Scale the image
                    int scaledWidth = (int)(width * scale);
                    int scaledHeight = (int)(height * scale);
                    
                    Image scaledImage = originalIcon.getImage().getScaledInstance(
                        scaledWidth, scaledHeight, Image.SCALE_SMOOTH);
                    
                    // Display the image
                    imagePreviewLabel.setIcon(new ImageIcon(scaledImage));
                    
                    // Show image info in text area
                    StringBuilder info = new StringBuilder();
                    info.append("[Image file] ").append(file.getName()).append("\n");
                    info.append("Size: ").append(formatFileSize(file.length())).append("\n");
                    info.append("Dimensions: ").append(width).append(" x ")
                        .append(height).append(" pixels\n");
                    info.append("Last modified: ").append(new java.util.Date(file.lastModified()));
                    
                    previewTextArea.setText(info.toString());
                    
                    // Make image preview visible
                    imageScrollPane.setVisible(true);
                    
                } catch (Exception ex) {
                    // If we can't display the image, show metadata
                    showFileMetadata(file, fileType);
                }
            } else if (fileType.equals("Text")) {
                // For text files, show content preview
                try {
                    String content = new String(Files.readAllBytes(Paths.get(filePath)));
                    if (content.length() > 1000) {
                        content = content.substring(0, 1000) + "... (file truncated)";
                    }
                    previewTextArea.setText(content);
                } catch (Exception ex) {
                    showFileMetadata(file, fileType);
                }
            } else {
                // For other binary files, show metadata
                showFileMetadata(file, fileType);
            }
            
            statusLabel.setText("Status: File preview loaded");
            
        } catch (Exception e) {
            previewTextArea.setText("Error reading file: " + e.getMessage());
            imagePreviewLabel.setIcon(null);
            imageScrollPane.setVisible(false);
        }
    }

    private void showFileMetadata(File file, String fileType) {
        StringBuilder info = new StringBuilder();
        info.append("[").append(fileType).append(" file] ").append(file.getName()).append("\n");
        info.append("Size: ").append(formatFileSize(file.length())).append("\n");
        info.append("Last modified: ").append(new java.util.Date(file.lastModified())).append("\n");
        previewTextArea.setText(info.toString());
    }

    private void updateProgress(int value, String message) {
        progressBar.setValue(value);
        progressBar.setString(message);
        statusLabel.setText("Status: " + message);
    }

    private void handleGenerateKey(ActionEvent e) {
        try {
            updateProgress(0, "Generating key...");
            SecretKey key = generateKey(currentAlgorithm, currentKeySize);
            saveKey(key, keyFileField.getText());
            updateProgress(100, "Key generated successfully");
            JOptionPane.showMessageDialog(this, "New key generated and saved.");
        } catch (Exception ex) {
            updateProgress(0, "Error: " + ex.getMessage());
            showError(ex);
        }
    }

    private void handleEncryptFile(ActionEvent e) {
        try {
            updateProgress(0, "Loading key...");
            SecretKey key = loadKey(keyFileField.getText(), currentAlgorithm);
            updateProgress(10, "Encrypting file...");
            
            // Use instance method instead of static method
            encryptFile(inputFileField.getText(), encryptedFileField.getText(), key, currentAlgorithm);
            
            updateProgress(100, "File encrypted successfully");
            JOptionPane.showMessageDialog(this, "File encrypted successfully.");
            updatePreview(encryptedFileField.getText());
        } catch (Exception ex) {
            updateProgress(0, "Error: " + ex.getMessage());
            showError(ex);
        }
    }

    private void handleDecryptFile(ActionEvent e) {
        try {
            updateProgress(0, "Loading key...");
            SecretKey key = loadKey(keyFileField.getText(), currentAlgorithm);
            updateProgress(10, "Decrypting file...");
            
            decryptFile(encryptedFileField.getText(), decryptedFileField.getText(), key, currentAlgorithm);
            
            updateProgress(100, "File decrypted successfully");
            JOptionPane.showMessageDialog(this, "File decrypted successfully.");
            
            // Force refresh the preview for the decrypted file
            String decryptedPath = decryptedFileField.getText();
            updatePreview(decryptedPath);
            
            // Explicitly check if it's an image and make sure it's displayed
            File decryptedFile = new File(decryptedPath);
            if (decryptedFile.exists()) {
                String fileType = getFileType(decryptedFile.getName());
                if (fileType.equals("Image")) {
                    // Force image to display by explicitly loading it
                    try {
                        ImageIcon icon = new ImageIcon(decryptedPath);
                        if (icon.getIconWidth() > 0) {  // Valid image
                            // Scale image for display
                            Image img = icon.getImage();
                            int maxHeight = 300;
                            int maxWidth = 400;
                            double scale = 1.0;
                            
                            if (icon.getIconHeight() > maxHeight) {
                                scale = (double)maxHeight / icon.getIconHeight();
                            }
                            if (icon.getIconWidth() * scale > maxWidth) {
                                scale = (double)maxWidth / icon.getIconWidth();
                            }
                            
                            Image scaledImg = img.getScaledInstance(
                                (int)(icon.getIconWidth() * scale), 
                                (int)(icon.getIconHeight() * scale), 
                                Image.SCALE_SMOOTH);
                            
                            imagePreviewLabel.setIcon(new ImageIcon(scaledImg));
                            imageScrollPane.setVisible(true);
                            
                            // Update status
                            statusLabel.setText("Status: Decrypted image displayed");
                        }
                    } catch (Exception ex) {
                        System.out.println("Error displaying image: " + ex.getMessage());
                    }
                }
            }
        } catch (Exception ex) {
            updateProgress(0, "Error: " + ex.getMessage());
            showError(ex);
        }
    }

    private void showError(Exception e) {
        JOptionPane.showMessageDialog(this, "Error: " + e.getMessage(), "Error", JOptionPane.ERROR_MESSAGE);
        e.printStackTrace();
    }

    public static SecretKey generateKey(String algorithm, int keySize) throws Exception {
        KeyGenerator keyGen = KeyGenerator.getInstance(algorithm);
        keyGen.init(keySize, new SecureRandom());
        return keyGen.generateKey();
    }

    public static void saveKey(SecretKey key, String filePath) throws IOException {
        byte[] encodedKey = key.getEncoded();
        Files.write(Paths.get(filePath), Base64.getEncoder().encode(encodedKey));
    }

    public static SecretKey loadKey(String filePath, String algorithm) throws IOException {
        byte[] encodedKey = Base64.getDecoder().decode(Files.readAllBytes(Paths.get(filePath)));
        return new SecretKeySpec(encodedKey, algorithm);
    }

    // Update buffer size for better memory efficiency
    private static final int BUFFER_SIZE = 8192; // Increased from 4096 for better performance

    // Add file type detection
    private static final String[] IMAGE_EXTENSIONS = {".png", ".jpg", ".jpeg", ".gif", ".bmp"};
    private static final String[] DOCUMENT_EXTENSIONS = {".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx"};
    private static final String[] ARCHIVE_EXTENSIONS = {".zip", ".rar", ".7z", ".tar", ".gz"};

    // Improved file type detection
    private String getFileType(String fileName) {
        fileName = fileName.toLowerCase();
        
        for (String ext : IMAGE_EXTENSIONS) {
            if (fileName.endsWith(ext)) return "Image";
        }
        
        for (String ext : DOCUMENT_EXTENSIONS) {
            if (fileName.endsWith(ext)) return "Document";
        }
        
        for (String ext : ARCHIVE_EXTENSIONS) {
            if (fileName.endsWith(ext)) return "Archive";
        }
        
        if (isTextFile(fileName)) return "Text";
        
        return "Binary";
    }

    // Improved encryption with memory usage reporting
    public void encryptFile(String inputFile, String outputFile, SecretKey key, String algorithm) throws Exception {
        Cipher cipher = Cipher.getInstance(algorithm);
        cipher.init(Cipher.ENCRYPT_MODE, key);
        
        File inFile = new File(inputFile);
        long fileSize = inFile.length();
        long processedBytes = 0;
        
        try (FileInputStream fis = new FileInputStream(inputFile);
             FileOutputStream fos = new FileOutputStream(outputFile)) {
            
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            
            while ((bytesRead = fis.read(buffer)) != -1) {
                byte[] outputBytes = cipher.update(buffer, 0, bytesRead);
                if (outputBytes != null) {
                    fos.write(outputBytes);
                }
                
                // Update progress
                processedBytes += bytesRead;
                int progressPercent = (int)((processedBytes * 100) / fileSize);
                updateProgress(progressPercent, "Encrypting: " + progressPercent + "%");
            }
            
            byte[] outputBytes = cipher.doFinal();
            if (outputBytes != null) {
                fos.write(outputBytes);
            }
            
            // Log memory usage
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = runtime.totalMemory() - runtime.freeMemory();
            System.out.println("Memory used: " + (usedMemory / 1024 / 1024) + " MB");
        }
    }

    // Improved decryption with memory usage reporting
    public void decryptFile(String inputFile, String outputFile, SecretKey key, String algorithm) throws Exception {
        Cipher cipher = Cipher.getInstance(algorithm);
        cipher.init(Cipher.DECRYPT_MODE, key);
        
        File inFile = new File(inputFile);
        long fileSize = inFile.length();
        long processedBytes = 0;
        
        try (FileInputStream fis = new FileInputStream(inputFile);
             FileOutputStream fos = new FileOutputStream(outputFile)) {
            
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            
            while ((bytesRead = fis.read(buffer)) != -1) {
                byte[] outputBytes = cipher.update(buffer, 0, bytesRead);
                if (outputBytes != null) {
                    fos.write(outputBytes);
                }
                
                // Update progress
                processedBytes += bytesRead;
                int progressPercent = (int)((processedBytes * 100) / fileSize);
                updateProgress(progressPercent, "Decrypting: " + progressPercent + "%");
            }
            
            byte[] outputBytes = cipher.doFinal();
            if (outputBytes != null) {
                fos.write(outputBytes);
            }
            
            // Log memory usage
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = runtime.totalMemory() - runtime.freeMemory();
            System.out.println("Memory used: " + (usedMemory / 1024 / 1024) + " MB");
        }
    }

    private boolean isTextFile(String fileName) {
        String[] textExtensions = {".txt", ".java", ".html", ".xml", ".json", ".csv", ".md"};
        for (String ext : textExtensions) {
            if (fileName.endsWith(ext)) return true;
        }
        return false;
    }

    // Helper method to format file size
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " bytes";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024 * 1024));
        }
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            FileEncryptorGUI app = new FileEncryptorGUI();
            app.setVisible(true);
        });
    }
}
