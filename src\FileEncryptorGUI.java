import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.SecureRandom;
import java.util.Base64;

public class FileEncryptorGUI extends JFrame {

    private static final String[] ALGORITHMS = {"AES", "DES", "Blowfish"};
    private static final int[] KEY_SIZES = {128, 192, 256};

    private String currentAlgorithm = "AES";
    private int currentKeySize = 256;

    private JTextField inputFileField, encryptedFileField, decryptedFileField, keyFileField;
    private JButton generateKeyButton, encryptButton, decryptButton;
    private JButton browseInputButton, browseEncryptedButton, browseDecryptedButton, browseKeyButton;
    private JButton downloadEncryptedButton, downloadDecryptedButton;
    private JButton showKeyButton, validateKeyButton;
    private JLabel keyValidationLabel;
    private JTextArea keyDisplayArea;
    private String lastGeneratedKey = null;
    private JComboBox<String> algorithmComboBox;
    private JComboBox<Integer> keySizeComboBox;
    private JProgressBar progressBar;
    private JTextArea previewTextArea;
    private JToggleButton darkModeToggle;
    private JLabel statusLabel;
    private JLabel imagePreviewLabel;
    private JScrollPane imageScrollPane;
    private JPanel previewPanel;

    public FileEncryptorGUI() {
        setTitle("File Encryptor");
        setSize(800, 600);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);

        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(new EmptyBorder(10, 10, 10, 10));

        JPanel settingsPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        algorithmComboBox = new JComboBox<>(ALGORITHMS);
        algorithmComboBox.setSelectedItem(currentAlgorithm);

        keySizeComboBox = new JComboBox<>();
        for (int size : KEY_SIZES) keySizeComboBox.addItem(size);
        keySizeComboBox.setSelectedItem(currentKeySize);

        darkModeToggle = new JToggleButton("Dark Mode");

        settingsPanel.add(new JLabel("Algorithm:"));
        settingsPanel.add(algorithmComboBox);
        settingsPanel.add(new JLabel("Key Size:"));
        settingsPanel.add(keySizeComboBox);
        settingsPanel.add(darkModeToggle);

        JPanel filePanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.insets = new Insets(5, 5, 5, 5);

        gbc.gridx = 0; gbc.gridy = 0;
        filePanel.add(new JLabel("Input File:"), gbc);
        gbc.gridx = 1; gbc.weightx = 1.0;
        inputFileField = new JTextField("input.txt", 20);
        filePanel.add(inputFileField, gbc);
        gbc.gridx = 2; gbc.weightx = 0.0;
        browseInputButton = new JButton("Browse...");
        filePanel.add(browseInputButton, gbc);

        gbc.gridx = 0; gbc.gridy = 1;
        filePanel.add(new JLabel("Encrypted File:"), gbc);
        gbc.gridx = 1;
        encryptedFileField = new JTextField("encrypt.txt", 20);
        filePanel.add(encryptedFileField, gbc);
        gbc.gridx = 2;
        browseEncryptedButton = new JButton("Browse...");
        filePanel.add(browseEncryptedButton, gbc);
        gbc.gridx = 3;
        downloadEncryptedButton = new JButton("Download");
        downloadEncryptedButton.setEnabled(false);
        downloadEncryptedButton.setToolTipText("Download the encrypted file to a location of your choice");
        filePanel.add(downloadEncryptedButton, gbc);

        gbc.gridx = 0; gbc.gridy = 2;
        filePanel.add(new JLabel("Decrypted File:"), gbc);
        gbc.gridx = 1;
        decryptedFileField = new JTextField("decryp.txt", 20);
        filePanel.add(decryptedFileField, gbc);
        gbc.gridx = 2;
        browseDecryptedButton = new JButton("Browse...");
        filePanel.add(browseDecryptedButton, gbc);
        gbc.gridx = 3;
        downloadDecryptedButton = new JButton("Download");
        downloadDecryptedButton.setEnabled(false);
        filePanel.add(downloadDecryptedButton, gbc);

        gbc.gridx = 0; gbc.gridy = 3;
        filePanel.add(new JLabel("Key File:"), gbc);
        gbc.gridx = 1;
        keyFileField = new JTextField("secret.key", 20);
        filePanel.add(keyFileField, gbc);
        gbc.gridx = 2;
        browseKeyButton = new JButton("Browse...");
        filePanel.add(browseKeyButton, gbc);
        gbc.gridx = 3;
        validateKeyButton = new JButton("Validate");
        validateKeyButton.setToolTipText("Validate if the key matches the encrypted file");
        filePanel.add(validateKeyButton, gbc);

        // Key validation status row
        gbc.gridx = 0; gbc.gridy = 4;
        filePanel.add(new JLabel("Key Status:"), gbc);
        gbc.gridx = 1; gbc.gridwidth = 2;
        keyValidationLabel = new JLabel("No validation performed");
        keyValidationLabel.setForeground(Color.GRAY);
        filePanel.add(keyValidationLabel, gbc);
        gbc.gridx = 3; gbc.gridwidth = 1;
        showKeyButton = new JButton("Show Key");
        showKeyButton.setEnabled(false);
        showKeyButton.setToolTipText("Display the encryption key content");
        filePanel.add(showKeyButton, gbc);

        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 0));
        generateKeyButton = new JButton("Generate Key");
        generateKeyButton.setToolTipText("Generate a new encryption key and save it to the key file");

        encryptButton = new JButton("Encrypt File");
        encryptButton.setToolTipText("Encrypt the input file using the selected key and algorithm");

        decryptButton = new JButton("Decrypt File");
        decryptButton.setToolTipText("Decrypt the encrypted file using the matching key");

        buttonPanel.add(generateKeyButton);
        buttonPanel.add(encryptButton);
        buttonPanel.add(decryptButton);

        previewPanel = new JPanel(new BorderLayout());
        previewPanel.setBorder(BorderFactory.createTitledBorder("File Preview"));

        // Create tabbed pane for different preview types
        JTabbedPane previewTabs = new JTabbedPane();

        // Text preview tab
        previewTextArea = new JTextArea(8, 40);
        previewTextArea.setEditable(false);
        JScrollPane textScrollPane = new JScrollPane(previewTextArea);
        previewTabs.addTab("File Info", textScrollPane);

        // Image preview tab
        imagePreviewLabel = new JLabel();
        imagePreviewLabel.setHorizontalAlignment(JLabel.CENTER);
        imageScrollPane = new JScrollPane(imagePreviewLabel);
        previewTabs.addTab("Image Preview", imageScrollPane);

        // Key display tab
        keyDisplayArea = new JTextArea(8, 40);
        keyDisplayArea.setEditable(false);
        keyDisplayArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        keyDisplayArea.setBackground(new Color(245, 245, 245));
        JScrollPane keyScrollPane = new JScrollPane(keyDisplayArea);
        previewTabs.addTab("Encryption Key", keyScrollPane);

        previewPanel.add(previewTabs, BorderLayout.CENTER);

        JPanel progressPanel = new JPanel(new BorderLayout(5, 5));
        progressBar = new JProgressBar(0, 100);
        progressBar.setStringPainted(true);
        progressBar.setString("Ready");
        statusLabel = new JLabel("Status: Ready");
        progressPanel.add(progressBar, BorderLayout.CENTER);
        progressPanel.add(statusLabel, BorderLayout.SOUTH);

        mainPanel.add(settingsPanel, BorderLayout.NORTH);
        mainPanel.add(filePanel, BorderLayout.CENTER);
        JPanel southPanel = new JPanel(new BorderLayout());
        southPanel.add(buttonPanel, BorderLayout.NORTH);
        southPanel.add(previewPanel, BorderLayout.CENTER);
        southPanel.add(progressPanel, BorderLayout.SOUTH);
        mainPanel.add(southPanel, BorderLayout.SOUTH);

        add(mainPanel);

        generateKeyButton.addActionListener(this::handleGenerateKey);
        encryptButton.addActionListener(this::handleEncryptFile);
        decryptButton.addActionListener(this::handleDecryptFile);

        browseInputButton.addActionListener(e -> browseFile(inputFileField));
        browseEncryptedButton.addActionListener(e -> browseFile(encryptedFileField));
        browseDecryptedButton.addActionListener(e -> browseFile(decryptedFileField));
        browseKeyButton.addActionListener(e -> browseFile(keyFileField));

        // Download button listeners
        downloadEncryptedButton.addActionListener(e -> downloadFile(encryptedFileField.getText(), "encrypted"));
        downloadDecryptedButton.addActionListener(e -> downloadFile(decryptedFileField.getText(), "decrypted"));

        // Key management button listeners
        showKeyButton.addActionListener(e -> handleShowKey());
        validateKeyButton.addActionListener(e -> handleValidateKey());

        // Add key field change listener for real-time validation
        keyFileField.addActionListener(e -> performKeyValidation());
        keyFileField.getDocument().addDocumentListener(new javax.swing.event.DocumentListener() {
            public void insertUpdate(javax.swing.event.DocumentEvent e) {
                SwingUtilities.invokeLater(() -> performKeyValidation());
            }
            public void removeUpdate(javax.swing.event.DocumentEvent e) {
                SwingUtilities.invokeLater(() -> performKeyValidation());
            }
            public void changedUpdate(javax.swing.event.DocumentEvent e) {
                SwingUtilities.invokeLater(() -> performKeyValidation());
            }
        });

        algorithmComboBox.addActionListener(e -> {
            currentAlgorithm = (String) algorithmComboBox.getSelectedItem();
            updateKeySizeOptions();
        });

        keySizeComboBox.addActionListener(e -> {
            if (keySizeComboBox.getSelectedItem() != null) {
                currentKeySize = (Integer) keySizeComboBox.getSelectedItem();
            }
        });

        darkModeToggle.addActionListener(e -> toggleDarkMode(darkModeToggle.isSelected()));
        inputFileField.addActionListener(e -> updatePreview(inputFileField.getText()));
    }

    private void browseFile(JTextField textField) {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setCurrentDirectory(new File(System.getProperty("user.home")));

        // Set up file filters based on the field type
        if (textField == inputFileField) {
            // For input files, allow all supported types
            FileNameExtensionFilter allFilter = new FileNameExtensionFilter(
                "All Supported Files", "txt", "png", "jpg", "jpeg", "bmp", "gif", "pdf", "doc", "docx"
            );
            FileNameExtensionFilter imageFilter = new FileNameExtensionFilter(
                "Image Files", "png", "jpg", "jpeg", "bmp", "gif"
            );
            FileNameExtensionFilter textFilter = new FileNameExtensionFilter(
                "Text Files", "txt", "java", "html", "xml", "json", "csv", "md"
            );

            fileChooser.addChoosableFileFilter(allFilter);
            fileChooser.addChoosableFileFilter(imageFilter);
            fileChooser.addChoosableFileFilter(textFilter);
            fileChooser.setFileFilter(allFilter);
        } else if (textField == keyFileField) {
            // For key files, prefer .key extension
            FileNameExtensionFilter keyFilter = new FileNameExtensionFilter(
                "Key Files (*.key)", "key"
            );
            fileChooser.setFileFilter(keyFilter);
        }

        fileChooser.setAcceptAllFileFilterUsed(true);

        int result = fileChooser.showOpenDialog(this);
        if (result == JFileChooser.APPROVE_OPTION) {
            File selectedFile = fileChooser.getSelectedFile();
            String filePath = selectedFile.getAbsolutePath();

            // Clean the file path to avoid trailing characters
            filePath = filePath.trim().replaceAll("[<>]", "");

            textField.setText(filePath);

            if (textField == inputFileField) {
                // Validate file type for input files
                if (validateInputFile(selectedFile)) {
                    updatePreview(filePath);
                } else {
                    JOptionPane.showMessageDialog(this,
                        "Unsupported file type. Please select a text or image file.",
                        "Invalid File Type", JOptionPane.WARNING_MESSAGE);
                    textField.setText("");
                }
            }
        }
    }

    private boolean validateInputFile(File file) {
        if (!file.exists() || !file.canRead()) {
            return false;
        }

        String fileName = file.getName().toLowerCase();
        String fileType = getFileType(fileName);

        // Allow text files, images, and some document types
        return fileType.equals("Text") || fileType.equals("Image") || fileType.equals("Document");
    }

    private void downloadFile(String sourceFilePath, String fileType) {
        try {
            File sourceFile = new File(sourceFilePath);
            if (!sourceFile.exists()) {
                JOptionPane.showMessageDialog(this,
                    "File does not exist: " + sourceFilePath,
                    "File Not Found", JOptionPane.ERROR_MESSAGE);
                return;
            }

            JFileChooser saveChooser = new JFileChooser();
            saveChooser.setDialogTitle("Save " + fileType + " file");
            saveChooser.setCurrentDirectory(new File(System.getProperty("user.home") + "/Downloads"));

            // Set suggested filename
            String originalName = sourceFile.getName();
            String suggestedName = fileType + "_" + originalName;
            saveChooser.setSelectedFile(new File(suggestedName));

            // Set file filters based on file type
            String detectedType = getFileType(originalName);
            if (detectedType.equals("Image")) {
                FileNameExtensionFilter imageFilter = new FileNameExtensionFilter(
                    "Image Files", "png", "jpg", "jpeg", "bmp", "gif"
                );
                saveChooser.setFileFilter(imageFilter);
            }

            int result = saveChooser.showSaveDialog(this);
            if (result == JFileChooser.APPROVE_OPTION) {
                File destinationFile = saveChooser.getSelectedFile();

                // Copy the file
                updateProgress(0, "Downloading file...");
                Files.copy(sourceFile.toPath(), destinationFile.toPath(),
                          java.nio.file.StandardCopyOption.REPLACE_EXISTING);

                updateProgress(100, "File downloaded successfully");
                JOptionPane.showMessageDialog(this,
                    "File saved to: " + destinationFile.getAbsolutePath(),
                    "Download Complete", JOptionPane.INFORMATION_MESSAGE);
            }
        } catch (Exception e) {
            updateProgress(0, "Download failed");
            showError(e);
        }
    }

    private void updateKeySizeOptions() {
        keySizeComboBox.removeAllItems();
        switch (currentAlgorithm) {
            case "AES":
                keySizeComboBox.addItem(128);
                keySizeComboBox.addItem(192);
                keySizeComboBox.addItem(256);
                break;
            case "DES":
                keySizeComboBox.addItem(56);
                break;
            case "Blowfish":
                keySizeComboBox.addItem(32);
                keySizeComboBox.addItem(64);
                keySizeComboBox.addItem(128);
                keySizeComboBox.addItem(256);
                keySizeComboBox.addItem(448);
                break;
        }
        if (keySizeComboBox.getItemCount() > 0) {
            keySizeComboBox.setSelectedIndex(0);
            currentKeySize = (Integer) keySizeComboBox.getSelectedItem();
        }
    }

    private void toggleDarkMode(boolean darkMode) {
        try {
            if (darkMode) {
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
                SwingUtilities.updateComponentTreeUI(this);
                previewTextArea.setBackground(Color.DARK_GRAY);
                previewTextArea.setForeground(Color.WHITE);
                statusLabel.setText("Status: Dark Mode Enabled");
            } else {
                UIManager.setLookAndFeel(UIManager.getCrossPlatformLookAndFeelClassName());
                SwingUtilities.updateComponentTreeUI(this);
                previewTextArea.setBackground(Color.WHITE);
                previewTextArea.setForeground(Color.BLACK);
                statusLabel.setText("Status: Light Mode Enabled");
            }
        } catch (Exception e) {
            showError(e);
        }
    }

    private void updatePreview(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists() || !file.isFile() || !file.canRead()) {
                previewTextArea.setText("File not found or cannot be read: " + filePath);
                imagePreviewLabel.setIcon(null);
                imageScrollPane.setVisible(false);
                return;
            }
            
            String fileType = getFileType(file.getName());
            
            // Reset preview components
            imagePreviewLabel.setIcon(null);
            imageScrollPane.setVisible(false);
            
            if (fileType.equals("Image")) {
                try {
                    // Load and display the image
                    ImageIcon originalIcon = new ImageIcon(filePath);
                    
                    // Check if it's a valid image
                    if (originalIcon.getIconWidth() <= 0) {
                        showFileMetadata(file, fileType);
                        return;
                    }
                    
                    // Get dimensions
                    int width = originalIcon.getIconWidth();
                    int height = originalIcon.getIconHeight();
                    
                    // Calculate scaling
                    int maxHeight = 300;
                    int maxWidth = 400;
                    double scale = 1.0;
                    
                    if (height > maxHeight) {
                        scale = (double)maxHeight / height;
                    }
                    if (width * scale > maxWidth) {
                        scale = (double)maxWidth / width;
                    }
                    
                    // Scale the image
                    int scaledWidth = (int)(width * scale);
                    int scaledHeight = (int)(height * scale);
                    
                    Image scaledImage = originalIcon.getImage().getScaledInstance(
                        scaledWidth, scaledHeight, Image.SCALE_SMOOTH);
                    
                    // Display the image
                    imagePreviewLabel.setIcon(new ImageIcon(scaledImage));
                    
                    // Show image info in text area
                    StringBuilder info = new StringBuilder();
                    info.append("[Image file] ").append(file.getName()).append("\n");
                    info.append("Size: ").append(formatFileSize(file.length())).append("\n");
                    info.append("Dimensions: ").append(width).append(" x ")
                        .append(height).append(" pixels\n");
                    info.append("Last modified: ").append(new java.util.Date(file.lastModified()));
                    
                    previewTextArea.setText(info.toString());
                    
                    // Make image preview visible
                    imageScrollPane.setVisible(true);
                    
                } catch (Exception ex) {
                    // If we can't display the image, show metadata
                    showFileMetadata(file, fileType);
                }
            } else if (fileType.equals("Text")) {
                // For text files, show content preview
                try {
                    String content = new String(Files.readAllBytes(Paths.get(filePath)));
                    if (content.length() > 1000) {
                        content = content.substring(0, 1000) + "... (file truncated)";
                    }
                    previewTextArea.setText(content);
                } catch (Exception ex) {
                    showFileMetadata(file, fileType);
                }
            } else {
                // For other binary files, show metadata
                showFileMetadata(file, fileType);
            }
            
            statusLabel.setText("Status: File preview loaded");
            
        } catch (Exception e) {
            previewTextArea.setText("Error reading file: " + e.getMessage());
            imagePreviewLabel.setIcon(null);
            imageScrollPane.setVisible(false);
        }
    }

    private void showFileMetadata(File file, String fileType) {
        StringBuilder info = new StringBuilder();
        info.append("[").append(fileType).append(" file] ").append(file.getName()).append("\n");
        info.append("Size: ").append(formatFileSize(file.length())).append("\n");
        info.append("Last modified: ").append(new java.util.Date(file.lastModified())).append("\n");
        previewTextArea.setText(info.toString());
    }

    private void updateProgress(int value, String message) {
        progressBar.setValue(value);
        progressBar.setString(message);
        statusLabel.setText("Status: " + message);
    }

    private void handleShowKey() {
        try {
            String keyPath = keyFileField.getText().trim();
            if (keyPath.isEmpty()) {
                JOptionPane.showMessageDialog(this,
                    "Please specify a key file path first.",
                    "No Key File", JOptionPane.WARNING_MESSAGE);
                return;
            }

            File keyFile = new File(keyPath);
            if (!keyFile.exists()) {
                JOptionPane.showMessageDialog(this,
                    "Key file does not exist: " + keyPath,
                    "Key File Not Found", JOptionPane.ERROR_MESSAGE);
                return;
            }

            // Read and display the key
            byte[] keyBytes = Files.readAllBytes(Paths.get(keyPath));
            String keyContent = new String(keyBytes);

            // Display in the key tab
            keyDisplayArea.setText("Key File: " + keyFile.getName() + "\n" +
                                 "Key Size: " + keyBytes.length + " bytes\n" +
                                 "Algorithm: " + currentAlgorithm + "\n\n" +
                                 "Key Content (Base64):\n" + keyContent);

            // Also show in a popup for easy copying
            JTextArea popupArea = new JTextArea(10, 50);
            popupArea.setText(keyContent);
            popupArea.setEditable(false);
            popupArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
            popupArea.setCaretPosition(0);

            JScrollPane scrollPane = new JScrollPane(popupArea);

            JPanel panel = new JPanel(new BorderLayout());
            panel.add(new JLabel("Encryption Key (Base64 encoded):"), BorderLayout.NORTH);
            panel.add(scrollPane, BorderLayout.CENTER);

            JButton copyButton = new JButton("Copy to Clipboard");
            copyButton.addActionListener(e -> {
                java.awt.datatransfer.StringSelection stringSelection =
                    new java.awt.datatransfer.StringSelection(keyContent);
                java.awt.Toolkit.getDefaultToolkit().getSystemClipboard()
                    .setContents(stringSelection, null);
                JOptionPane.showMessageDialog(this, "Key copied to clipboard!");
            });

            JPanel buttonPanel = new JPanel();
            buttonPanel.add(copyButton);
            panel.add(buttonPanel, BorderLayout.SOUTH);

            JOptionPane.showMessageDialog(this, panel, "Encryption Key", JOptionPane.INFORMATION_MESSAGE);

        } catch (Exception e) {
            showError(e);
        }
    }

    private void handleValidateKey() {
        performKeyValidation();
    }

    private void performKeyValidation() {
        try {
            String keyPath = keyFileField.getText().trim();
            String encryptedPath = encryptedFileField.getText().trim();

            if (keyPath.isEmpty() || encryptedPath.isEmpty()) {
                updateKeyValidationStatus("No validation performed", Color.GRAY, false);
                return;
            }

            File keyFile = new File(keyPath);
            File encryptedFile = new File(encryptedPath);

            if (!keyFile.exists()) {
                updateKeyValidationStatus("Key file not found", Color.RED, false);
                return;
            }

            if (!encryptedFile.exists()) {
                updateKeyValidationStatus("Encrypted file not found", Color.RED, false);
                return;
            }

            // Validate key by attempting to read it
            try {
                SecretKey key = loadKey(keyPath, currentAlgorithm);

                // Try to decrypt a small portion to validate the key
                boolean isValid = validateKeyForFile(key, encryptedPath);

                if (isValid) {
                    updateKeyValidationStatus("✓ Key is valid", Color.GREEN, true);
                    downloadDecryptedButton.setEnabled(true);
                } else {
                    updateKeyValidationStatus("✗ Key does not match encrypted file", Color.RED, false);
                    downloadDecryptedButton.setEnabled(false);
                }

            } catch (Exception e) {
                updateKeyValidationStatus("✗ Invalid key format", Color.RED, false);
                downloadDecryptedButton.setEnabled(false);
            }

        } catch (Exception e) {
            updateKeyValidationStatus("Validation error", Color.RED, false);
        }
    }

    private boolean validateKeyForFile(SecretKey key, String encryptedFilePath) {
        try {
            // Create a temporary file for validation
            File tempFile = File.createTempFile("validation", ".tmp");
            tempFile.deleteOnExit();

            // Try to decrypt just the first few bytes
            Cipher cipher = Cipher.getInstance(currentAlgorithm);
            cipher.init(Cipher.DECRYPT_MODE, key);

            try (FileInputStream fis = new FileInputStream(encryptedFilePath)) {
                byte[] buffer = new byte[Math.min(1024, fis.available())]; // Read first 1KB or less
                int bytesRead = fis.read(buffer);

                if (bytesRead > 0) {
                    // Try to decrypt the sample
                    cipher.update(buffer, 0, bytesRead);
                    return true; // If no exception, key is likely valid
                }
            }

            return false;
        } catch (Exception e) {
            return false; // If any exception occurs, key is invalid
        }
    }

    private void updateKeyValidationStatus(String message, Color color, boolean enableShowKey) {
        keyValidationLabel.setText(message);
        keyValidationLabel.setForeground(color);
        showKeyButton.setEnabled(enableShowKey || new File(keyFileField.getText().trim()).exists());
    }

    private void handleGenerateKey(ActionEvent e) {
        try {
            updateProgress(0, "Generating key...");
            SecretKey key = generateKey(currentAlgorithm, currentKeySize);
            String keyPath = keyFileField.getText();
            saveKey(key, keyPath);

            // Store the key content for display
            byte[] keyBytes = Files.readAllBytes(Paths.get(keyPath));
            lastGeneratedKey = new String(keyBytes);

            // Display the key in the key tab
            keyDisplayArea.setText("Key File: " + new File(keyPath).getName() + "\n" +
                                 "Key Size: " + keyBytes.length + " bytes\n" +
                                 "Algorithm: " + currentAlgorithm + "\n" +
                                 "Key Strength: " + currentKeySize + " bits\n\n" +
                                 "Key Content (Base64):\n" + lastGeneratedKey);

            updateProgress(100, "Key generated successfully");

            // Enable show key button
            showKeyButton.setEnabled(true);
            updateKeyValidationStatus("New key generated", Color.BLUE, true);

            // Show key in popup after generation
            int choice = JOptionPane.showConfirmDialog(this,
                "Key generated successfully!\n\nWould you like to view the key now?",
                "Key Generated",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.INFORMATION_MESSAGE);

            if (choice == JOptionPane.YES_OPTION) {
                handleShowKey();
            }

        } catch (Exception ex) {
            updateProgress(0, "Error: " + ex.getMessage());
            showError(ex);
        }
    }

    private void handleEncryptFile(ActionEvent e) {
        try {
            // Validate input file first
            File inputFile = new File(inputFileField.getText());
            if (!validateInputFile(inputFile)) {
                JOptionPane.showMessageDialog(this,
                    "Please select a valid input file (text or image).",
                    "Invalid Input File", JOptionPane.WARNING_MESSAGE);
                return;
            }

            updateProgress(0, "Loading key...");
            SecretKey key = loadKey(keyFileField.getText(), currentAlgorithm);
            updateProgress(10, "Encrypting file...");

            // Generate output filename with preserved extension info
            String outputPath = generateEncryptedFilename(inputFileField.getText(), encryptedFileField.getText());
            encryptedFileField.setText(outputPath);

            // Use instance method instead of static method
            encryptFile(inputFileField.getText(), outputPath, key, currentAlgorithm);

            updateProgress(100, "File encrypted successfully");

            // Enable download button for encrypted file
            downloadEncryptedButton.setEnabled(true);

            // Display the encryption key after successful encryption
            try {
                byte[] keyBytes = Files.readAllBytes(Paths.get(keyFileField.getText()));
                String keyContent = new String(keyBytes);

                keyDisplayArea.setText("Encryption completed!\n\n" +
                                     "Key File: " + new File(keyFileField.getText()).getName() + "\n" +
                                     "Algorithm: " + currentAlgorithm + "\n" +
                                     "Key Size: " + currentKeySize + " bits\n" +
                                     "Encrypted File: " + new File(outputPath).getName() + "\n\n" +
                                     "Key Content (Base64):\n" + keyContent);

                // Enable show key button
                showKeyButton.setEnabled(true);

                // Perform validation to enable decrypt button if key matches
                performKeyValidation();

            } catch (Exception keyEx) {
                System.out.println("Could not display key: " + keyEx.getMessage());
            }

            // Show success message with option to view key
            String fileType = getFileType(inputFileField.getText());
            String message = "File encrypted successfully!";

            if (fileType.equals("Image")) {
                message += "\n\nThe encryption key is now displayed in the 'Encryption Key' tab.";

                int choice = JOptionPane.showConfirmDialog(this,
                    message + "\n\nWould you like to view the encryption key?",
                    "Encryption Complete",
                    JOptionPane.YES_NO_OPTION,
                    JOptionPane.INFORMATION_MESSAGE);

                if (choice == JOptionPane.YES_OPTION) {
                    handleShowKey();
                }
            } else {
                JOptionPane.showMessageDialog(this, message);
            }

            updatePreview(outputPath);
        } catch (Exception ex) {
            updateProgress(0, "Error: " + ex.getMessage());
            showError(ex);
        }
    }

    private String generateEncryptedFilename(String inputPath, String currentOutputPath) {
        File inputFile = new File(inputPath);
        String inputName = inputFile.getName();
        String inputExtension = "";

        int lastDot = inputName.lastIndexOf('.');
        if (lastDot > 0) {
            inputExtension = inputName.substring(lastDot);
        }

        // If current output path is default, generate a better name
        if (currentOutputPath.equals("encrypt.txt")) {
            String baseName = lastDot > 0 ? inputName.substring(0, lastDot) : inputName;
            return baseName + "_encrypted" + inputExtension + ".enc";
        }

        return currentOutputPath;
    }

    private String generateDecryptedFilename(String encryptedPath, String currentOutputPath) {
        File encryptedFile = new File(encryptedPath);
        String encryptedName = encryptedFile.getName();

        // If current output path is default, generate a better name
        if (currentOutputPath.equals("decryp.txt")) {
            if (encryptedName.endsWith(".enc")) {
                // Remove .enc and _encrypted suffix if present
                String baseName = encryptedName.substring(0, encryptedName.length() - 4);
                if (baseName.endsWith("_encrypted")) {
                    baseName = baseName.substring(0, baseName.length() - 10);
                }
                return baseName + "_decrypted";
            } else {
                return encryptedName + "_decrypted";
            }
        }

        return currentOutputPath;
    }

    private void handleDecryptFile(ActionEvent e) {
        try {
            // Validate encrypted file exists
            File encryptedFile = new File(encryptedFileField.getText());
            if (!encryptedFile.exists()) {
                JOptionPane.showMessageDialog(this,
                    "Encrypted file does not exist: " + encryptedFileField.getText(),
                    "File Not Found", JOptionPane.ERROR_MESSAGE);
                return;
            }

            // Validate key before attempting decryption
            updateProgress(0, "Validating key...");
            SecretKey key;
            try {
                key = loadKey(keyFileField.getText(), currentAlgorithm);

                // Validate that the key matches the encrypted file
                if (!validateKeyForFile(key, encryptedFileField.getText())) {
                    JOptionPane.showMessageDialog(this,
                        "The selected key does not match the encrypted file.\n" +
                        "Please verify you have the correct key file.",
                        "Key Validation Failed", JOptionPane.ERROR_MESSAGE);
                    updateKeyValidationStatus("✗ Key does not match encrypted file", Color.RED, false);
                    return;
                }

                updateKeyValidationStatus("✓ Key validated successfully", Color.GREEN, true);

            } catch (Exception keyEx) {
                JOptionPane.showMessageDialog(this,
                    "Invalid key file or format:\n" + keyEx.getMessage(),
                    "Key Error", JOptionPane.ERROR_MESSAGE);
                updateKeyValidationStatus("✗ Invalid key format", Color.RED, false);
                return;
            }

            updateProgress(20, "Decrypting file...");

            // Generate output filename with preserved extension info
            String outputPath = generateDecryptedFilename(encryptedFileField.getText(), decryptedFileField.getText());
            decryptedFileField.setText(outputPath);

            decryptFile(encryptedFileField.getText(), outputPath, key, currentAlgorithm);

            updateProgress(100, "File decrypted successfully");

            // Enable download button for decrypted file
            downloadDecryptedButton.setEnabled(true);

            JOptionPane.showMessageDialog(this, "File decrypted successfully.");

            // Force refresh the preview for the decrypted file
            String decryptedPath = decryptedFileField.getText();
            updatePreview(decryptedPath);

            // Explicitly check if it's an image and make sure it's displayed
            File decryptedFile = new File(decryptedPath);
            if (decryptedFile.exists()) {
                String fileType = getFileType(decryptedFile.getName());
                if (fileType.equals("Image")) {
                    // Force image to display by explicitly loading it
                    try {
                        ImageIcon icon = new ImageIcon(decryptedPath);
                        if (icon.getIconWidth() > 0) {  // Valid image
                            // Scale image for display
                            Image img = icon.getImage();
                            int maxHeight = 300;
                            int maxWidth = 400;
                            double scale = 1.0;

                            if (icon.getIconHeight() > maxHeight) {
                                scale = (double)maxHeight / icon.getIconHeight();
                            }
                            if (icon.getIconWidth() * scale > maxWidth) {
                                scale = (double)maxWidth / icon.getIconWidth();
                            }

                            Image scaledImg = img.getScaledInstance(
                                (int)(icon.getIconWidth() * scale),
                                (int)(icon.getIconHeight() * scale),
                                Image.SCALE_SMOOTH);

                            imagePreviewLabel.setIcon(new ImageIcon(scaledImg));
                            imageScrollPane.setVisible(true);

                            // Update status
                            statusLabel.setText("Status: Decrypted image displayed");
                        }
                    } catch (Exception ex) {
                        System.out.println("Error displaying image: " + ex.getMessage());
                    }
                }
            }
        } catch (Exception ex) {
            updateProgress(0, "Error: " + ex.getMessage());
            showError(ex);
        }
    }

    private void showError(Exception e) {
        String errorMessage = e.getMessage();
        String errorTitle = "Error";

        // Provide more specific error messages
        if (e instanceof FileNotFoundException) {
            errorTitle = "File Not Found";
            errorMessage = "The specified file could not be found:\n" + errorMessage;
        } else if (e instanceof IOException) {
            errorTitle = "File I/O Error";
            if (errorMessage.contains("trailing char")) {
                errorMessage = "Invalid file path. Please check the file path and try again.";
            } else if (errorMessage.contains("key")) {
                errorMessage = "Key file error: " + errorMessage;
            } else {
                errorMessage = "File operation failed: " + errorMessage;
            }
        } else if (e instanceof IllegalArgumentException) {
            errorTitle = "Invalid Input";
            errorMessage = "Invalid input provided: " + errorMessage;
        } else if (e instanceof SecurityException) {
            errorTitle = "Security Error";
            errorMessage = "Access denied: " + errorMessage;
        }

        JOptionPane.showMessageDialog(this, errorMessage, errorTitle, JOptionPane.ERROR_MESSAGE);
        e.printStackTrace();
    }

    public static SecretKey generateKey(String algorithm, int keySize) throws Exception {
        KeyGenerator keyGen = KeyGenerator.getInstance(algorithm);
        keyGen.init(keySize, new SecureRandom());
        return keyGen.generateKey();
    }

    public static void saveKey(SecretKey key, String filePath) throws IOException {
        // Clean the file path to remove any trailing characters
        filePath = filePath.trim().replaceAll("[<>]", "");

        try {
            byte[] encodedKey = key.getEncoded();
            Files.write(Paths.get(filePath), Base64.getEncoder().encode(encodedKey));
        } catch (Exception e) {
            throw new IOException("Failed to save key to file: " + filePath, e);
        }
    }

    public static SecretKey loadKey(String filePath, String algorithm) throws IOException {
        // Clean the file path to remove any trailing characters
        filePath = filePath.trim().replaceAll("[<>]", "");

        File keyFile = new File(filePath);
        if (!keyFile.exists()) {
            throw new IOException("Key file does not exist: " + filePath);
        }

        if (!keyFile.canRead()) {
            throw new IOException("Cannot read key file: " + filePath);
        }

        try {
            byte[] encodedKey = Base64.getDecoder().decode(Files.readAllBytes(Paths.get(filePath)));
            return new SecretKeySpec(encodedKey, algorithm);
        } catch (IllegalArgumentException e) {
            throw new IOException("Invalid key file format. The key file may be corrupted or not a valid Base64 encoded key.", e);
        }
    }

    // Update buffer size for better memory efficiency
    private static final int BUFFER_SIZE = 8192; // Increased from 4096 for better performance

    // Add file type detection
    private static final String[] IMAGE_EXTENSIONS = {".png", ".jpg", ".jpeg", ".gif", ".bmp"};
    private static final String[] DOCUMENT_EXTENSIONS = {".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx"};
    private static final String[] ARCHIVE_EXTENSIONS = {".zip", ".rar", ".7z", ".tar", ".gz"};

    // Improved file type detection
    private String getFileType(String fileName) {
        fileName = fileName.toLowerCase();
        
        for (String ext : IMAGE_EXTENSIONS) {
            if (fileName.endsWith(ext)) return "Image";
        }
        
        for (String ext : DOCUMENT_EXTENSIONS) {
            if (fileName.endsWith(ext)) return "Document";
        }
        
        for (String ext : ARCHIVE_EXTENSIONS) {
            if (fileName.endsWith(ext)) return "Archive";
        }
        
        if (isTextFile(fileName)) return "Text";
        
        return "Binary";
    }

    // Improved encryption with memory usage reporting
    public void encryptFile(String inputFile, String outputFile, SecretKey key, String algorithm) throws Exception {
        Cipher cipher = Cipher.getInstance(algorithm);
        cipher.init(Cipher.ENCRYPT_MODE, key);
        
        File inFile = new File(inputFile);
        long fileSize = inFile.length();
        long processedBytes = 0;
        
        try (FileInputStream fis = new FileInputStream(inputFile);
             FileOutputStream fos = new FileOutputStream(outputFile)) {
            
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            
            while ((bytesRead = fis.read(buffer)) != -1) {
                byte[] outputBytes = cipher.update(buffer, 0, bytesRead);
                if (outputBytes != null) {
                    fos.write(outputBytes);
                }
                
                // Update progress
                processedBytes += bytesRead;
                int progressPercent = (int)((processedBytes * 100) / fileSize);
                updateProgress(progressPercent, "Encrypting: " + progressPercent + "%");
            }
            
            byte[] outputBytes = cipher.doFinal();
            if (outputBytes != null) {
                fos.write(outputBytes);
            }
            
            // Log memory usage
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = runtime.totalMemory() - runtime.freeMemory();
            System.out.println("Memory used: " + (usedMemory / 1024 / 1024) + " MB");
        }
    }

    // Improved decryption with memory usage reporting
    public void decryptFile(String inputFile, String outputFile, SecretKey key, String algorithm) throws Exception {
        Cipher cipher = Cipher.getInstance(algorithm);
        cipher.init(Cipher.DECRYPT_MODE, key);
        
        File inFile = new File(inputFile);
        long fileSize = inFile.length();
        long processedBytes = 0;
        
        try (FileInputStream fis = new FileInputStream(inputFile);
             FileOutputStream fos = new FileOutputStream(outputFile)) {
            
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            
            while ((bytesRead = fis.read(buffer)) != -1) {
                byte[] outputBytes = cipher.update(buffer, 0, bytesRead);
                if (outputBytes != null) {
                    fos.write(outputBytes);
                }
                
                // Update progress
                processedBytes += bytesRead;
                int progressPercent = (int)((processedBytes * 100) / fileSize);
                updateProgress(progressPercent, "Decrypting: " + progressPercent + "%");
            }
            
            byte[] outputBytes = cipher.doFinal();
            if (outputBytes != null) {
                fos.write(outputBytes);
            }
            
            // Log memory usage
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = runtime.totalMemory() - runtime.freeMemory();
            System.out.println("Memory used: " + (usedMemory / 1024 / 1024) + " MB");
        }
    }

    private boolean isTextFile(String fileName) {
        String[] textExtensions = {".txt", ".java", ".html", ".xml", ".json", ".csv", ".md"};
        for (String ext : textExtensions) {
            if (fileName.endsWith(ext)) return true;
        }
        return false;
    }

    // Helper method to format file size
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " bytes";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024 * 1024));
        }
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            FileEncryptorGUI app = new FileEncryptorGUI();
            app.setVisible(true);
        });
    }
}
