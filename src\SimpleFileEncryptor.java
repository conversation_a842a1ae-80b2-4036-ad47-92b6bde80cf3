import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.SecureRandom;
import java.util.Base64;

public class SimpleFileEncryptor extends JFrame {

    private String currentAlgorithm = "AES";
    private int currentKeySize = 256;

    private JTextField inputFileField;
    private JButton generateKeyButton, encryptButton, decryptButton;
    private JButton browseInputButton;
    private JTextArea keyDisplayArea, keyInputArea;
    private JLabel keyValidationLabel;
    private JProgressBar progressBar;
    private JLabel statusLabel;

    public SimpleFileEncryptor() {
        setTitle("Simple File Encryptor");
        setSize(600, 450);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);

        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(new EmptyBorder(15, 15, 15, 15));

        // File selection panel
        JPanel filePanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.insets = new Insets(8, 8, 8, 8);

        // Input file row
        gbc.gridx = 0; gbc.gridy = 0;
        filePanel.add(new JLabel("Select File:"), gbc);
        gbc.gridx = 1; gbc.weightx = 1.0;
        inputFileField = new JTextField(25);
        inputFileField.setToolTipText("Select a file to encrypt or decrypt");
        filePanel.add(inputFileField, gbc);
        gbc.gridx = 2; gbc.weightx = 0.0;
        browseInputButton = new JButton("Browse...");
        browseInputButton.setToolTipText("Browse for file to encrypt/decrypt");
        filePanel.add(browseInputButton, gbc);

        // Key input section for decryption
        gbc.gridx = 0; gbc.gridy = 1;
        filePanel.add(new JLabel("Decryption Key:"), gbc);
        gbc.gridx = 1; gbc.gridwidth = 2;
        keyInputArea = new JTextArea(3, 30);
        keyInputArea.setToolTipText("Paste the Base64 encoded encryption key here for decryption");
        keyInputArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 11));
        keyInputArea.setBorder(BorderFactory.createLoweredBevelBorder());
        keyInputArea.setLineWrap(true);
        keyInputArea.setWrapStyleWord(false);
        JScrollPane keyInputScrollPane = new JScrollPane(keyInputArea);
        keyInputScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        filePanel.add(keyInputScrollPane, gbc);
        
        // Key validation status row
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 1;
        filePanel.add(new JLabel("Status:"), gbc);
        gbc.gridx = 1; gbc.gridwidth = 2;
        keyValidationLabel = new JLabel("Ready");
        keyValidationLabel.setForeground(Color.GRAY);
        filePanel.add(keyValidationLabel, gbc);

        // Button panel
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 15, 0));
        generateKeyButton = new JButton("Generate Key");
        generateKeyButton.setToolTipText("Generate a new encryption key");
        
        encryptButton = new JButton("Encrypt File");
        encryptButton.setToolTipText("Encrypt the selected file");
        
        decryptButton = new JButton("Decrypt File");
        decryptButton.setToolTipText("Decrypt using the entered key");
        decryptButton.setEnabled(false); // Initially disabled
        
        buttonPanel.add(generateKeyButton);
        buttonPanel.add(encryptButton);
        buttonPanel.add(decryptButton);

        // Key display area
        keyDisplayArea = new JTextArea(6, 50);
        keyDisplayArea.setEditable(false);
        keyDisplayArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 11));
        keyDisplayArea.setBackground(new Color(245, 245, 245));
        keyDisplayArea.setBorder(BorderFactory.createTitledBorder("Generated Key (Copy this for decryption)"));
        keyDisplayArea.setLineWrap(true);
        keyDisplayArea.setWrapStyleWord(false);
        JScrollPane keyScrollPane = new JScrollPane(keyDisplayArea);

        // Progress and status
        JPanel progressPanel = new JPanel(new BorderLayout(5, 5));
        progressBar = new JProgressBar(0, 100);
        progressBar.setStringPainted(true);
        progressBar.setString("Ready");
        statusLabel = new JLabel("Status: Ready");
        progressPanel.add(progressBar, BorderLayout.CENTER);
        progressPanel.add(statusLabel, BorderLayout.SOUTH);

        // Layout
        mainPanel.add(filePanel, BorderLayout.NORTH);
        mainPanel.add(buttonPanel, BorderLayout.CENTER);
        
        JPanel bottomPanel = new JPanel(new BorderLayout());
        bottomPanel.add(keyScrollPane, BorderLayout.CENTER);
        bottomPanel.add(progressPanel, BorderLayout.SOUTH);
        mainPanel.add(bottomPanel, BorderLayout.SOUTH);

        add(mainPanel);

        // Action listeners
        generateKeyButton.addActionListener(this::handleGenerateKey);
        encryptButton.addActionListener(this::handleEncryptFile);
        decryptButton.addActionListener(this::handleDecryptFile);
        browseInputButton.addActionListener(e -> browseFile());

        // Key input area listener for real-time validation
        keyInputArea.getDocument().addDocumentListener(new javax.swing.event.DocumentListener() {
            public void insertUpdate(javax.swing.event.DocumentEvent e) {
                SwingUtilities.invokeLater(() -> validateKeyInput());
            }
            public void removeUpdate(javax.swing.event.DocumentEvent e) {
                SwingUtilities.invokeLater(() -> validateKeyInput());
            }
            public void changedUpdate(javax.swing.event.DocumentEvent e) {
                SwingUtilities.invokeLater(() -> validateKeyInput());
            }
        });
    }

    private void browseFile() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setCurrentDirectory(new File(System.getProperty("user.home")));
        
        FileNameExtensionFilter allFilter = new FileNameExtensionFilter(
            "All Supported Files", "txt", "png", "jpg", "jpeg", "bmp", "gif", "pdf", "doc", "docx"
        );
        fileChooser.setFileFilter(allFilter);
        fileChooser.setAcceptAllFileFilterUsed(true);

        int result = fileChooser.showOpenDialog(this);
        if (result == JFileChooser.APPROVE_OPTION) {
            File selectedFile = fileChooser.getSelectedFile();
            String filePath = selectedFile.getAbsolutePath().trim().replaceAll("[<>]", "");
            inputFileField.setText(filePath);
        }
    }

    private void validateKeyInput() {
        try {
            String keyInput = keyInputArea.getText().trim();
            String inputFilePath = inputFileField.getText().trim();
            
            if (keyInput.isEmpty()) {
                updateKeyValidationStatus("No key entered", Color.GRAY);
                decryptButton.setEnabled(false);
                return;
            }
            
            if (inputFilePath.isEmpty()) {
                updateKeyValidationStatus("No input file selected", Color.GRAY);
                decryptButton.setEnabled(false);
                return;
            }
            
            File inputFile = new File(inputFilePath);
            if (!inputFile.exists()) {
                updateKeyValidationStatus("Input file not found", Color.RED);
                decryptButton.setEnabled(false);
                return;
            }
            
            // Validate key format (Base64)
            try {
                byte[] keyBytes = Base64.getDecoder().decode(keyInput);
                SecretKey key = new SecretKeySpec(keyBytes, currentAlgorithm);
                
                updateKeyValidationStatus("✓ Key format is valid", Color.GREEN);
                decryptButton.setEnabled(true);
                
            } catch (IllegalArgumentException e) {
                updateKeyValidationStatus("✗ Invalid Base64 key format", Color.RED);
                decryptButton.setEnabled(false);
            }
            
        } catch (Exception e) {
            updateKeyValidationStatus("Validation error", Color.RED);
            decryptButton.setEnabled(false);
        }
    }

    private void updateKeyValidationStatus(String message, Color color) {
        keyValidationLabel.setText(message);
        keyValidationLabel.setForeground(color);
    }

    private void updateProgress(int value, String message) {
        progressBar.setValue(value);
        progressBar.setString(message);
        statusLabel.setText("Status: " + message);
    }

    private void handleGenerateKey(ActionEvent e) {
        try {
            updateProgress(0, "Generating key...");
            SecretKey key = generateKey(currentAlgorithm, currentKeySize);
            
            // Create temporary key file
            File tempKeyFile = File.createTempFile("temp_key", ".key");
            tempKeyFile.deleteOnExit();
            saveKey(key, tempKeyFile.getAbsolutePath());
            
            // Read and display the key
            byte[] keyBytes = Files.readAllBytes(tempKeyFile.toPath());
            String keyContent = new String(keyBytes);
            
            keyDisplayArea.setText("Algorithm: " + currentAlgorithm + "\n" +
                                 "Key Size: " + currentKeySize + " bits\n\n" +
                                 "Key Content (Base64):\n" + keyContent);
            
            updateProgress(100, "Key generated successfully");
            updateKeyValidationStatus("New key generated", Color.BLUE);
            
            JOptionPane.showMessageDialog(this, 
                "Key generated successfully!\nCopy the key from the text area below for decryption.", 
                "Key Generated", JOptionPane.INFORMATION_MESSAGE);
            
        } catch (Exception ex) {
            updateProgress(0, "Error: " + ex.getMessage());
            showError(ex);
        }
    }

    private void handleEncryptFile(ActionEvent e) {
        try {
            File inputFile = new File(inputFileField.getText());
            if (!inputFile.exists()) {
                JOptionPane.showMessageDialog(this, 
                    "Please select a valid input file.", 
                    "Invalid Input File", JOptionPane.WARNING_MESSAGE);
                return;
            }
            
            updateProgress(0, "Generating key...");
            SecretKey key = generateKey(currentAlgorithm, currentKeySize);
            
            updateProgress(20, "Encrypting file...");
            String outputPath = generateEncryptedFilename(inputFileField.getText());
            encryptFile(inputFileField.getText(), outputPath, key, currentAlgorithm);
            
            // Display the encryption key
            byte[] keyBytes = key.getEncoded();
            String keyContent = Base64.getEncoder().encodeToString(keyBytes);
            
            keyDisplayArea.setText("Encryption completed!\n\n" +
                                 "Input File: " + inputFile.getName() + "\n" +
                                 "Encrypted File: " + new File(outputPath).getName() + "\n" +
                                 "Algorithm: " + currentAlgorithm + "\n\n" +
                                 "Key Content (Base64):\n" + keyContent);
            
            updateProgress(100, "File encrypted successfully");
            
            JOptionPane.showMessageDialog(this, 
                "File encrypted successfully!\nEncrypted file: " + outputPath + 
                "\n\nCopy the key from the text area for decryption.");
            
            // Update the input field to point to the encrypted file for decryption
            inputFileField.setText(outputPath);
            
        } catch (Exception ex) {
            updateProgress(0, "Error: " + ex.getMessage());
            showError(ex);
        }
    }

    private void handleDecryptFile(ActionEvent e) {
        try {
            String inputFilePath = inputFileField.getText().trim();
            File inputFile = new File(inputFilePath);
            if (!inputFile.exists()) {
                JOptionPane.showMessageDialog(this, 
                    "Input file does not exist: " + inputFilePath, 
                    "File Not Found", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            String keyInput = keyInputArea.getText().trim();
            if (keyInput.isEmpty()) {
                JOptionPane.showMessageDialog(this, 
                    "Please enter the decryption key.", 
                    "No Key Provided", JOptionPane.WARNING_MESSAGE);
                return;
            }
            
            updateProgress(0, "Validating key...");
            SecretKey key;
            try {
                byte[] keyBytes = Base64.getDecoder().decode(keyInput);
                key = new SecretKeySpec(keyBytes, currentAlgorithm);
            } catch (IllegalArgumentException keyEx) {
                JOptionPane.showMessageDialog(this, 
                    "Invalid key format. Please ensure the key is properly Base64 encoded.", 
                    "Key Format Error", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            updateProgress(20, "Decrypting file...");
            String outputPath = generateDecryptedFilename(inputFilePath);
            decryptFile(inputFilePath, outputPath, key, currentAlgorithm);
            
            updateProgress(100, "File decrypted successfully");
            
            JOptionPane.showMessageDialog(this, 
                "File decrypted successfully!\nDecrypted file: " + outputPath);
            
            // Update the input field to point to the decrypted file
            inputFileField.setText(outputPath);
            
            // Clear the key input area for security
            keyInputArea.setText("");
            updateKeyValidationStatus("Decryption completed", Color.BLUE);
            
        } catch (Exception ex) {
            updateProgress(0, "Error: " + ex.getMessage());
            showError(ex);
        }
    }

    private String generateEncryptedFilename(String inputPath) {
        File inputFile = new File(inputPath);
        String inputName = inputFile.getName();
        String inputExtension = "";
        
        int lastDot = inputName.lastIndexOf('.');
        if (lastDot > 0) {
            inputExtension = inputName.substring(lastDot);
        }
        
        String baseName = lastDot > 0 ? inputName.substring(0, lastDot) : inputName;
        return baseName + "_encrypted" + inputExtension + ".enc";
    }
    
    private String generateDecryptedFilename(String encryptedPath) {
        File encryptedFile = new File(encryptedPath);
        String encryptedName = encryptedFile.getName();
        
        if (encryptedName.endsWith(".enc")) {
            String baseName = encryptedName.substring(0, encryptedName.length() - 4);
            if (baseName.endsWith("_encrypted")) {
                baseName = baseName.substring(0, baseName.length() - 10);
            }
            return baseName + "_decrypted";
        } else {
            int lastDot = encryptedName.lastIndexOf('.');
            if (lastDot > 0) {
                String baseName = encryptedName.substring(0, lastDot);
                String extension = encryptedName.substring(lastDot);
                return baseName + "_decrypted" + extension;
            } else {
                return encryptedName + "_decrypted";
            }
        }
    }

    private void showError(Exception e) {
        String errorMessage = e.getMessage();
        String errorTitle = "Error";
        
        if (e instanceof FileNotFoundException) {
            errorTitle = "File Not Found";
            errorMessage = "The specified file could not be found:\n" + errorMessage;
        } else if (e instanceof IOException) {
            errorTitle = "File I/O Error";
            errorMessage = "File operation failed: " + errorMessage;
        }
        
        JOptionPane.showMessageDialog(this, errorMessage, errorTitle, JOptionPane.ERROR_MESSAGE);
        e.printStackTrace();
    }

    // Utility Methods
    public static SecretKey generateKey(String algorithm, int keySize) throws Exception {
        KeyGenerator keyGen = KeyGenerator.getInstance(algorithm);
        keyGen.init(keySize, new SecureRandom());
        return keyGen.generateKey();
    }

    public static void saveKey(SecretKey key, String filePath) throws IOException {
        filePath = filePath.trim().replaceAll("[<>]", "");
        try {
            byte[] encodedKey = key.getEncoded();
            Files.write(Paths.get(filePath), Base64.getEncoder().encode(encodedKey));
        } catch (Exception e) {
            throw new IOException("Failed to save key to file: " + filePath, e);
        }
    }

    public static void encryptFile(String inputFile, String outputFile, SecretKey key, String algorithm) throws Exception {
        Cipher cipher = Cipher.getInstance(algorithm);
        cipher.init(Cipher.ENCRYPT_MODE, key);
        byte[] inputBytes = Files.readAllBytes(Paths.get(inputFile));
        byte[] outputBytes = cipher.doFinal(inputBytes);
        Files.write(Paths.get(outputFile), outputBytes);
    }

    public static void decryptFile(String inputFile, String outputFile, SecretKey key, String algorithm) throws Exception {
        Cipher cipher = Cipher.getInstance(algorithm);
        cipher.init(Cipher.DECRYPT_MODE, key);
        byte[] inputBytes = Files.readAllBytes(Paths.get(inputFile));
        byte[] outputBytes = cipher.doFinal(inputBytes);
        Files.write(Paths.get(outputFile), outputBytes);
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
            } catch (Exception e) {
                e.printStackTrace();
            }
            new SimpleFileEncryptor().setVisible(true);
        });
    }
}
