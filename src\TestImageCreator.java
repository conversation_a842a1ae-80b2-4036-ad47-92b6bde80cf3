import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import javax.imageio.ImageIO;

public class TestImageCreator {
    public static void main(String[] args) {
        try {
            // Create a simple test image
            int width = 400;
            int height = 300;
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = image.createGraphics();
            
            // Set background
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, width, height);
            
            // Draw some shapes
            g2d.setColor(Color.BLUE);
            g2d.fillOval(50, 50, 100, 100);
            
            g2d.setColor(Color.RED);
            g2d.fillRect(200, 50, 100, 100);
            
            g2d.setColor(Color.GREEN);
            g2d.fillOval(125, 150, 150, 100);
            
            // Add text
            g2d.setColor(Color.BLACK);
            g2d.setFont(new Font("Arial", Font.BOLD, 24));
            g2d.drawString("Test Image for Encryption", 50, 280);
            
            g2d.dispose();
            
            // Save the image
            File outputFile = new File("test_image.png");
            ImageIO.write(image, "png", outputFile);
            
            System.out.println("Test image created: " + outputFile.getAbsolutePath());
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
