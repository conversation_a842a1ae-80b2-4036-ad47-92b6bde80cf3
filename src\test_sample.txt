This is a sample text file for testing encryption and decryption.

The file contains multiple lines of text to ensure that the encryption
and decryption process works correctly with various file sizes.

Features tested:
1. Text file encryption
2. Key generation and validation
3. Proper error handling for bad keys
4. Download functionality for decrypted files
5. Real-time key validation

This sample file should encrypt and decrypt successfully when using
the correct key, and show appropriate error messages when using
an incorrect key.
